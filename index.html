<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打字练习 - Typing Practice</title>
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- 预加载字体以提高性能 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <h1 class="title">打字练习</h1>
            <div class="header-controls">
                <!-- 主题切换按钮 -->
                <button id="theme-toggle" class="btn btn-icon" title="切换主题">
                    <span class="theme-icon">🌙</span>
                </button>
                <!-- 设置按钮 -->
                <button id="settings-btn" class="btn btn-icon" title="设置">
                    <span class="settings-icon">⚙️</span>
                </button>
                <!-- 统计分析按钮 -->
                <button id="analytics-btn" class="btn btn-icon" title="统计分析">
                    <span class="analytics-icon">📊</span>
                </button>
            </div>
        </header>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <div class="stat-item">
                <span class="stat-label">速度</span>
                <span id="wpm-display" class="stat-value">0</span>
                <span class="stat-unit">WPM</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">准确率</span>
                <span id="accuracy-display" class="stat-value">100</span>
                <span class="stat-unit">%</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">时间</span>
                <span id="time-display" class="stat-value">0</span>
                <span class="stat-unit">秒</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">错误</span>
                <span id="errors-display" class="stat-value">0</span>
                <span class="stat-unit">个</span>
            </div>
        </div>

        <!-- 练习模式选择 -->
        <div class="mode-selector">
            <button id="words-mode" class="mode-btn active" data-mode="words">单词练习</button>
            <button id="sentences-mode" class="mode-btn" data-mode="sentences">句子练习</button>
            <button id="paragraphs-mode" class="mode-btn" data-mode="paragraphs">段落练习</button>
            <button id="custom-mode" class="mode-btn" data-mode="custom">自定义</button>
        </div>

        <!-- 主要练习区域 -->
        <main class="practice-area">
            <!-- 文本显示区域 -->
            <div id="text-display" class="text-display">
                <!-- 动态生成的练习文本将显示在这里 -->
                <span class="loading">正在加载练习文本...</span>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
                <textarea 
                    id="typing-input" 
                    class="typing-input" 
                    placeholder="点击这里开始打字练习..."
                    autocomplete="off"
                    autocorrect="off"
                    autocapitalize="off"
                    spellcheck="false">
                </textarea>
            </div>

            <!-- 进度条 -->
            <div class="progress-container">
                <div id="progress-bar" class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <span id="progress-text" class="progress-text">0%</span>
            </div>
        </main>

        <!-- 控制按钮区域 -->
        <div class="controls">
            <button id="start-btn" class="btn btn-primary">开始练习</button>
            <button id="reset-btn" class="btn btn-secondary">重新开始</button>
            <button id="pause-btn" class="btn btn-secondary" disabled>暂停</button>
        </div>

        <!-- 虚拟键盘（可选显示） -->
        <div id="virtual-keyboard" class="virtual-keyboard hidden">
            <!-- 键盘布局将通过JavaScript动态生成 -->
        </div>

        <!-- 结果面板（练习完成后显示） -->
        <div id="results-panel" class="results-panel hidden">
            <h2>练习完成！</h2>
            <div class="results-stats">
                <div class="result-item">
                    <span class="result-label">最终速度:</span>
                    <span id="final-wpm" class="result-value">0 WPM</span>
                </div>
                <div class="result-item">
                    <span class="result-label">准确率:</span>
                    <span id="final-accuracy" class="result-value">0%</span>
                </div>
                <div class="result-item">
                    <span class="result-label">用时:</span>
                    <span id="final-time" class="result-value">0秒</span>
                </div>
                <div class="result-item">
                    <span class="result-label">错误数:</span>
                    <span id="final-errors" class="result-value">0个</span>
                </div>
            </div>
            <div class="results-actions">
                <button id="try-again-btn" class="btn btn-primary">再试一次</button>
                <button id="new-text-btn" class="btn btn-secondary">新文本</button>
            </div>
        </div>
    </div>

    <!-- 设置面板（模态框） -->
    <div id="settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>设置</h3>
                <button id="close-settings" class="btn btn-icon">✕</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="show-keyboard">显示虚拟键盘</label>
                    <input type="checkbox" id="show-keyboard">
                </div>
                <div class="setting-group">
                    <label for="sound-enabled">启用音效</label>
                    <input type="checkbox" id="sound-enabled">
                </div>
                <div class="setting-group">
                    <label for="finger-guide">手指指导模式</label>
                    <input type="checkbox" id="finger-guide">
                </div>
                <div class="setting-group">
                    <label for="text-size">文字大小</label>
                    <select id="text-size">
                        <option value="small">小</option>
                        <option value="medium" selected>中</option>
                        <option value="large">大</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="js/storage.js"></script>
    <script src="js/stats.js"></script>
    <script src="js/keyboard.js"></script>
    <script src="js/analytics.js"></script>
    <script src="js/practice-modes.js"></script>
    <script src="js/typing.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
