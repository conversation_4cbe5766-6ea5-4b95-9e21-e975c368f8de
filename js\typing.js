/**
 * 打字练习核心逻辑模块
 * 负责处理文本显示、输入检测和字符匹配
 */

class TypingManager {
    constructor() {
        // DOM元素引用
        this.elements = {
            textDisplay: document.getElementById('text-display'),
            typingInput: document.getElementById('typing-input'),
            startBtn: document.getElementById('start-btn'),
            resetBtn: document.getElementById('reset-btn'),
            pauseBtn: document.getElementById('pause-btn')
        };
        
        // 练习状态
        this.state = {
            currentText: '',
            userInput: '',
            currentIndex: 0,
            isActive: false,
            isPaused: false,
            isCompleted: false,
            mode: 'words' // words, sentences, paragraphs, custom
        };
        
        // 文本数据
        this.textData = {
            words: [],
            sentences: [],
            paragraphs: [],
            custom: ''
        };
        
        // 事件监听器
        this.eventListeners = new Map();
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化打字管理器
     */
    async init() {
        try {
            // 加载文本数据
            await this.loadTextData();
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 生成初始练习文本
            this.generateText();
            
            console.log('打字管理器初始化完成');
        } catch (error) {
            console.error('打字管理器初始化失败:', error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 加载文本数据
     */
    async loadTextData() {
        try {
            // 由于是静态文件，我们先使用内置的文本数据
            // 后续可以改为从JSON文件加载
            this.textData.words = [
                'the', 'quick', 'brown', 'fox', 'jumps', 'over', 'lazy', 'dog',
                'hello', 'world', 'javascript', 'programming', 'computer', 'keyboard',
                'typing', 'practice', 'speed', 'accuracy', 'finger', 'exercise',
                'development', 'website', 'application', 'function', 'variable',
                'array', 'object', 'string', 'number', 'boolean', 'method',
                'class', 'constructor', 'prototype', 'callback', 'promise',
                'async', 'await', 'event', 'listener', 'element', 'document'
            ];
            
            this.textData.sentences = [
                'The quick brown fox jumps over the lazy dog.',
                'JavaScript is a versatile programming language.',
                'Practice makes perfect in typing skills.',
                'Web development requires patience and dedication.',
                'Modern browsers support many advanced features.',
                'Responsive design adapts to different screen sizes.',
                'User experience is crucial for web applications.',
                'Clean code is easier to maintain and debug.',
                'Version control helps track changes in projects.',
                'Testing ensures software quality and reliability.'
            ];
            
            this.textData.paragraphs = [
                'JavaScript is a high-level, interpreted programming language that conforms to the ECMAScript specification. It is a language that is also characterized as dynamic, weakly typed, prototype-based and multi-paradigm. Alongside HTML and CSS, JavaScript is one of the core technologies of the World Wide Web.',
                'Web development is the work involved in developing a website for the Internet or an intranet. Web development can range from developing a simple single static page of plain text to complex web applications, electronic businesses, and social network services.',
                'Typing speed is measured in words per minute (WPM). The average typing speed is around 40 WPM, while professional typists can achieve speeds of 70 WPM or higher. Regular practice and proper finger positioning are key to improving typing speed and accuracy.'
            ];
            
            console.log('文本数据加载完成');
        } catch (error) {
            console.error('加载文本数据失败:', error);
            throw error;
        }
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 输入框事件
        if (this.elements.typingInput) {
            this.addEventListener(this.elements.typingInput, 'input', this.handleInput.bind(this));
            this.addEventListener(this.elements.typingInput, 'keydown', this.handleKeyDown.bind(this));
            this.addEventListener(this.elements.typingInput, 'focus', this.handleFocus.bind(this));
            this.addEventListener(this.elements.typingInput, 'blur', this.handleBlur.bind(this));
        }
        
        // 控制按钮事件
        if (this.elements.startBtn) {
            this.addEventListener(this.elements.startBtn, 'click', this.startPractice.bind(this));
        }
        
        if (this.elements.resetBtn) {
            this.addEventListener(this.elements.resetBtn, 'click', this.resetPractice.bind(this));
        }
        
        if (this.elements.pauseBtn) {
            this.addEventListener(this.elements.pauseBtn, 'click', this.togglePause.bind(this));
        }
        
        // 键盘快捷键
        this.addEventListener(document, 'keydown', this.handleGlobalKeyDown.bind(this));
        
        // 模式切换按钮
        const modeButtons = document.querySelectorAll('.mode-btn');
        modeButtons.forEach(btn => {
            this.addEventListener(btn, 'click', this.handleModeChange.bind(this));
        });
    }
    
    /**
     * 添加事件监听器并记录
     */
    addEventListener(element, event, handler) {
        element.addEventListener(event, handler);
        
        // 记录事件监听器以便后续清理
        const key = `${element.tagName}-${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        this.eventListeners.get(key).push({ element, event, handler });
    }
    
    /**
     * 清理所有事件监听器
     */
    removeAllEventListeners() {
        this.eventListeners.forEach(listeners => {
            listeners.forEach(({ element, event, handler }) => {
                element.removeEventListener(event, handler);
            });
        });
        this.eventListeners.clear();
    }
    
    /**
     * 生成练习文本
     * @param {string} mode - 练习模式
     */
    generateText(mode = this.state.mode) {
        this.state.mode = mode;
        let text = '';
        
        try {
            switch (mode) {
                case 'words':
                    // 随机选择25个单词
                    const selectedWords = this.getRandomItems(this.textData.words, 25);
                    text = selectedWords.join(' ');
                    break;
                    
                case 'sentences':
                    // 随机选择3-5个句子
                    const selectedSentences = this.getRandomItems(this.textData.sentences, 
                        Math.floor(Math.random() * 3) + 3);
                    text = selectedSentences.join(' ');
                    break;
                    
                case 'paragraphs':
                    // 随机选择1个段落
                    text = this.getRandomItems(this.textData.paragraphs, 1)[0];
                    break;
                    
                case 'custom':
                    text = this.textData.custom || '请在设置中输入自定义文本。';
                    break;
                    
                default:
                    text = this.getRandomItems(this.textData.words, 25).join(' ');
            }
            
            this.state.currentText = text;
            this.displayText();
            
            // 通知统计管理器文本长度
            if (window.statsManager) {
                statsManager.setTotalCharacters(text.length);
            }
            
            console.log(`生成${mode}模式文本:`, text.substring(0, 50) + '...');
        } catch (error) {
            console.error('生成文本失败:', error);
            this.showError('生成练习文本失败');
        }
    }
    
    /**
     * 从数组中随机选择指定数量的项目
     * @param {Array} array - 源数组
     * @param {number} count - 选择数量
     * @returns {Array} 随机选择的项目数组
     */
    getRandomItems(array, count) {
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, Math.min(count, array.length));
    }
    
    /**
     * 显示文本到页面
     */
    displayText() {
        if (!this.elements.textDisplay) return;
        
        const text = this.state.currentText;
        const currentIndex = this.state.currentIndex;
        
        // 创建字符元素
        let html = '';
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            let className = '';
            
            if (i < currentIndex) {
                // 已输入的字符
                if (i < this.state.userInput.length) {
                    className = this.state.userInput[i] === char ? 'char-correct' : 'char-incorrect';
                }
            } else if (i === currentIndex) {
                // 当前要输入的字符
                className = 'char-current';
            }
            
            // 处理空格显示
            const displayChar = char === ' ' ? '&nbsp;' : this.escapeHtml(char);
            html += `<span class="${className}">${displayChar}</span>`;
        }
        
        this.elements.textDisplay.innerHTML = html;
    }
    
    /**
     * HTML转义
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 处理输入事件
     * @param {Event} event - 输入事件
     */
    handleInput(event) {
        if (!this.state.isActive || this.state.isPaused) return;

        const inputValue = event.target.value;
        this.state.userInput = inputValue;
        this.state.currentIndex = inputValue.length;

        // 检查字符匹配
        this.checkCharacterMatch();

        // 更新显示
        this.displayText();

        // 检查是否完成
        if (this.state.currentIndex >= this.state.currentText.length) {
            this.completePractice();
        }
    }

    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyDown(event) {
        // 防止某些按键的默认行为
        if (event.key === 'Tab') {
            event.preventDefault();
        }

        // 如果练习未开始，自动开始
        if (!this.state.isActive && !this.state.isCompleted) {
            this.startPractice();
        }
    }

    /**
     * 处理输入框获得焦点
     */
    handleFocus() {
        if (this.elements.typingInput) {
            this.elements.typingInput.style.borderColor = 'var(--primary-color)';
        }
    }

    /**
     * 处理输入框失去焦点
     */
    handleBlur() {
        if (this.elements.typingInput) {
            this.elements.typingInput.style.borderColor = 'var(--border-color)';
        }
    }

    /**
     * 处理全局键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleGlobalKeyDown(event) {
        // 快捷键处理
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'Enter':
                    event.preventDefault();
                    this.startPractice();
                    break;
                case 'r':
                    event.preventDefault();
                    this.resetPractice();
                    break;
                case ' ':
                    event.preventDefault();
                    this.togglePause();
                    break;
            }
        }

        // Escape键暂停
        if (event.key === 'Escape') {
            this.togglePause();
        }
    }

    /**
     * 处理模式切换
     * @param {Event} event - 点击事件
     */
    handleModeChange(event) {
        const mode = event.target.dataset.mode;
        if (mode && mode !== this.state.mode) {
            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 重置练习并生成新文本
            this.resetPractice();
            this.generateText(mode);
        }
    }

    /**
     * 检查字符匹配
     */
    checkCharacterMatch() {
        const userInput = this.state.userInput;
        const targetText = this.state.currentText;

        if (userInput.length === 0) return;

        const lastCharIndex = userInput.length - 1;
        const userChar = userInput[lastCharIndex];
        const targetChar = targetText[lastCharIndex];

        const isCorrect = userChar === targetChar;

        // 通知统计管理器
        if (window.statsManager) {
            statsManager.updateCharacterStats(isCorrect);
        }

        // 播放音效（如果启用）
        this.playTypingSound(isCorrect);
    }

    /**
     * 播放打字音效
     * @param {boolean} isCorrect - 是否正确
     */
    playTypingSound(isCorrect) {
        // 检查是否启用音效
        if (!storageManager || !storageManager.getSetting('soundEnabled')) {
            return;
        }

        // 这里可以添加音效播放逻辑
        // 由于没有音频文件，暂时使用Web Audio API生成简单音效
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // 根据正确性设置不同频率
            oscillator.frequency.setValueAtTime(isCorrect ? 800 : 400, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
            // 音效播放失败，忽略错误
            console.warn('音效播放失败:', error);
        }
    }

    /**
     * 开始练习
     */
    startPractice() {
        if (this.state.isActive) return;

        this.state.isActive = true;
        this.state.isPaused = false;
        this.state.isCompleted = false;

        // 清空输入框并聚焦
        if (this.elements.typingInput) {
            this.elements.typingInput.value = '';
            this.elements.typingInput.focus();
            this.elements.typingInput.disabled = false;
        }

        // 重置状态
        this.state.userInput = '';
        this.state.currentIndex = 0;

        // 更新按钮状态
        this.updateButtonStates();

        // 开始统计
        if (window.statsManager) {
            statsManager.start(this.state.currentText.length);
        }

        // 更新显示
        this.displayText();

        console.log('练习开始');
    }

    /**
     * 重置练习
     */
    resetPractice() {
        this.state.isActive = false;
        this.state.isPaused = false;
        this.state.isCompleted = false;
        this.state.userInput = '';
        this.state.currentIndex = 0;

        // 清空输入框
        if (this.elements.typingInput) {
            this.elements.typingInput.value = '';
            this.elements.typingInput.disabled = false;
        }

        // 重置统计
        if (window.statsManager) {
            statsManager.resetStats();
        }

        // 更新按钮状态
        this.updateButtonStates();

        // 更新显示
        this.displayText();

        console.log('练习重置');
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        if (!this.state.isActive || this.state.isCompleted) return;

        if (this.state.isPaused) {
            // 恢复练习
            this.state.isPaused = false;

            if (this.elements.typingInput) {
                this.elements.typingInput.disabled = false;
                this.elements.typingInput.focus();
            }

            if (window.statsManager) {
                statsManager.resume();
            }

            console.log('练习恢复');
        } else {
            // 暂停练习
            this.state.isPaused = true;

            if (this.elements.typingInput) {
                this.elements.typingInput.disabled = true;
            }

            if (window.statsManager) {
                statsManager.pause();
            }

            console.log('练习暂停');
        }

        this.updateButtonStates();
    }

    /**
     * 完成练习
     */
    completePractice() {
        this.state.isActive = false;
        this.state.isCompleted = true;

        // 禁用输入框
        if (this.elements.typingInput) {
            this.elements.typingInput.disabled = true;
        }

        // 停止统计并获取结果
        let results = null;
        if (window.statsManager) {
            results = statsManager.stop();
        }

        // 保存练习记录
        if (results && window.storageManager) {
            storageManager.addHistoryRecord({
                mode: this.state.mode,
                text: this.state.currentText.substring(0, 100) + '...', // 只保存前100个字符
                ...results
            });
        }

        // 更新按钮状态
        this.updateButtonStates();

        // 显示结果
        this.showResults(results);

        console.log('练习完成:', results);
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        if (this.elements.startBtn) {
            this.elements.startBtn.disabled = this.state.isActive;
            this.elements.startBtn.textContent = this.state.isActive ? '练习中...' : '开始练习';
        }

        if (this.elements.pauseBtn) {
            this.elements.pauseBtn.disabled = !this.state.isActive || this.state.isCompleted;
            this.elements.pauseBtn.textContent = this.state.isPaused ? '继续' : '暂停';
        }

        if (this.elements.resetBtn) {
            this.elements.resetBtn.disabled = false;
        }
    }

    /**
     * 显示结果
     * @param {Object} results - 练习结果
     */
    showResults(results) {
        if (!results) return;

        // 更新结果面板
        const resultsPanel = document.getElementById('results-panel');
        if (resultsPanel) {
            // 更新结果数据
            const finalWPM = document.getElementById('final-wpm');
            const finalAccuracy = document.getElementById('final-accuracy');
            const finalTime = document.getElementById('final-time');
            const finalErrors = document.getElementById('final-errors');

            if (finalWPM) finalWPM.textContent = `${results.wpm} WPM`;
            if (finalAccuracy) finalAccuracy.textContent = `${results.accuracy}%`;
            if (finalTime) finalTime.textContent = `${results.time}秒`;
            if (finalErrors) finalErrors.textContent = `${results.errors}个`;

            // 显示结果面板
            resultsPanel.classList.remove('hidden');

            // 绑定结果面板按钮事件
            this.bindResultsEvents();
        }
    }

    /**
     * 绑定结果面板事件
     */
    bindResultsEvents() {
        const tryAgainBtn = document.getElementById('try-again-btn');
        const newTextBtn = document.getElementById('new-text-btn');

        if (tryAgainBtn) {
            tryAgainBtn.onclick = () => {
                this.hideResults();
                this.resetPractice();
            };
        }

        if (newTextBtn) {
            newTextBtn.onclick = () => {
                this.hideResults();
                this.resetPractice();
                this.generateText();
            };
        }
    }

    /**
     * 隐藏结果面板
     */
    hideResults() {
        const resultsPanel = document.getElementById('results-panel');
        if (resultsPanel) {
            resultsPanel.classList.add('hidden');
        }
    }

    /**
     * 设置自定义文本
     * @param {string} text - 自定义文本
     */
    setCustomText(text) {
        this.textData.custom = text;
        if (this.state.mode === 'custom') {
            this.generateText('custom');
        }
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态对象
     */
    getCurrentState() {
        return {
            ...this.state,
            progress: this.state.currentText.length > 0 ?
                Math.round((this.state.currentIndex / this.state.currentText.length) * 100) : 0
        };
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        console.error(message);

        // 在文本显示区域显示错误信息
        if (this.elements.textDisplay) {
            this.elements.textDisplay.innerHTML = `
                <div class="error-message" style="color: var(--error-color); text-align: center; padding: 2rem;">
                    <p>❌ ${message}</p>
                    <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: var(--primary-color); color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
                        刷新页面
                    </button>
                </div>
            `;
        }
    }

    /**
     * 销毁打字管理器
     */
    destroy() {
        // 停止所有活动
        this.state.isActive = false;
        this.state.isPaused = false;

        // 清理事件监听器
        this.removeAllEventListeners();

        // 清理统计
        if (window.statsManager) {
            statsManager.resetStats();
        }

        console.log('打字管理器已销毁');
    }
}

// 创建全局打字管理器实例
const typingManager = new TypingManager();
