/**
 * 练习模式管理器
 * 负责管理各种练习模式，包括难度等级、专项练习等
 */

class PracticeModeManager {
    constructor() {
        // 练习模式配置
        this.modes = {
            // 基础模式
            words: {
                name: '单词练习',
                description: '练习常用单词，提高基础打字能力',
                difficulties: {
                    beginner: { name: '初级', wordCount: 15, timeLimit: null },
                    intermediate: { name: '中级', wordCount: 25, timeLimit: null },
                    advanced: { name: '高级', wordCount: 40, timeLimit: null },
                    expert: { name: '专家', wordCount: 60, timeLimit: 120 }
                }
            },
            
            sentences: {
                name: '句子练习',
                description: '练习完整句子，提高连贯性',
                difficulties: {
                    beginner: { name: '初级', sentenceCount: 2, complexity: 'simple' },
                    intermediate: { name: '中级', sentenceCount: 3, complexity: 'medium' },
                    advanced: { name: '高级', sentenceCount: 5, complexity: 'complex' },
                    expert: { name: '专家', sentenceCount: 8, complexity: 'complex' }
                }
            },
            
            paragraphs: {
                name: '段落练习',
                description: '练习长文本，提高持续打字能力',
                difficulties: {
                    beginner: { name: '初级', length: 'short' },
                    intermediate: { name: '中级', length: 'medium' },
                    advanced: { name: '高级', length: 'long' },
                    expert: { name: '专家', length: 'very_long' }
                }
            },
            
            // 专项练习模式
            numbers: {
                name: '数字练习',
                description: '专门练习数字输入',
                difficulties: {
                    beginner: { name: '基础数字', pattern: 'simple', length: 20 },
                    intermediate: { name: '数字组合', pattern: 'mixed', length: 30 },
                    advanced: { name: '复杂数字', pattern: 'complex', length: 50 },
                    expert: { name: '数字挑战', pattern: 'extreme', length: 80 }
                }
            },
            
            symbols: {
                name: '符号练习',
                description: '练习标点符号和特殊字符',
                difficulties: {
                    beginner: { name: '基础符号', symbols: ['!', '?', '.', ','], length: 25 },
                    intermediate: { name: '常用符号', symbols: ['!', '?', '.', ',', ';', ':', '"', "'"], length: 35 },
                    advanced: { name: '编程符号', symbols: ['(', ')', '[', ']', '{', '}', '<', '>', '=', '+', '-', '*', '/'], length: 45 },
                    expert: { name: '全符号', symbols: ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '=', '-', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/'], length: 60 }
                }
            },
            
            programming: {
                name: '编程练习',
                description: '练习编程代码片段',
                difficulties: {
                    beginner: { name: 'HTML基础', language: 'html', complexity: 'basic' },
                    intermediate: { name: 'CSS样式', language: 'css', complexity: 'medium' },
                    advanced: { name: 'JavaScript', language: 'javascript', complexity: 'advanced' },
                    expert: { name: '混合代码', language: 'mixed', complexity: 'expert' }
                }
            },
            
            // 挑战模式
            speed: {
                name: '速度挑战',
                description: '在限定时间内尽可能快地打字',
                difficulties: {
                    beginner: { name: '30秒冲刺', timeLimit: 30, targetWPM: 20 },
                    intermediate: { name: '60秒冲刺', timeLimit: 60, targetWPM: 35 },
                    advanced: { name: '2分钟冲刺', timeLimit: 120, targetWPM: 50 },
                    expert: { name: '5分钟马拉松', timeLimit: 300, targetWPM: 70 }
                }
            },
            
            accuracy: {
                name: '准确率挑战',
                description: '追求极高的准确率',
                difficulties: {
                    beginner: { name: '95%准确率', targetAccuracy: 95, maxErrors: 3 },
                    intermediate: { name: '98%准确率', targetAccuracy: 98, maxErrors: 2 },
                    advanced: { name: '99%准确率', targetAccuracy: 99, maxErrors: 1 },
                    expert: { name: '完美打字', targetAccuracy: 100, maxErrors: 0 }
                }
            }
        };
        
        // 当前模式状态
        this.currentMode = 'words';
        this.currentDifficulty = 'beginner';
        this.customSettings = {};
        
        // 代码片段库
        this.codeSnippets = {
            html: {
                basic: [
                    '<!DOCTYPE html>\n<html>\n<head>\n<title>Page Title</title>\n</head>\n<body>\n<h1>Hello World</h1>\n</body>\n</html>',
                    '<div class="container">\n  <p>This is a paragraph.</p>\n  <a href="#" class="link">Click here</a>\n</div>',
                    '<form action="/submit" method="post">\n  <input type="text" name="username" required>\n  <button type="submit">Submit</button>\n</form>'
                ]
            },
            css: {
                medium: [
                    '.container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n}',
                    '@media (max-width: 768px) {\n  .responsive {\n    flex-direction: column;\n    padding: 1rem;\n  }\n}',
                    '.button {\n  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n}'
                ]
            },
            javascript: {
                advanced: [
                    'function fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}',
                    'const users = await fetch("/api/users")\n  .then(response => response.json())\n  .catch(error => console.error(error));',
                    'class Calculator {\n  constructor() {\n    this.result = 0;\n  }\n  add(value) {\n    this.result += value;\n    return this;\n  }\n}'
                ]
            }
        };
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化练习模式管理器
     */
    init() {
        this.createModeSelector();
        this.bindEvents();
        console.log('练习模式管理器初始化完成');
    }
    
    /**
     * 创建模式选择器
     */
    createModeSelector() {
        const modeSelector = document.querySelector('.mode-selector');
        if (!modeSelector) return;
        
        // 清空现有按钮
        modeSelector.innerHTML = '';
        
        // 创建模式按钮
        Object.entries(this.modes).forEach(([modeKey, mode]) => {
            const button = document.createElement('button');
            button.className = 'mode-btn';
            button.dataset.mode = modeKey;
            button.textContent = mode.name;
            button.title = mode.description;
            
            if (modeKey === this.currentMode) {
                button.classList.add('active');
            }
            
            modeSelector.appendChild(button);
        });
        
        // 添加难度选择器
        this.createDifficultySelector();
    }
    
    /**
     * 创建难度选择器
     */
    createDifficultySelector() {
        let difficultySelector = document.querySelector('.difficulty-selector');

        if (!difficultySelector) {
            difficultySelector = document.createElement('div');
            difficultySelector.className = 'difficulty-selector';

            // 插入到练习设置区域
            const practiceSettings = document.querySelector('.practice-settings');
            if (practiceSettings) {
                practiceSettings.appendChild(difficultySelector);
            }
        }

        difficultySelector.innerHTML = '';

        // 添加标题
        const title = document.createElement('div');
        title.className = 'difficulty-title';
        title.textContent = '难度等级';
        difficultySelector.appendChild(title);

        // 添加难度按钮
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'difficulty-buttons';

        const currentModeConfig = this.modes[this.currentMode];
        if (currentModeConfig && currentModeConfig.difficulties) {
            Object.entries(currentModeConfig.difficulties).forEach(([diffKey, diff]) => {
                const button = document.createElement('button');
                button.className = 'difficulty-btn';
                button.dataset.difficulty = diffKey;
                button.textContent = diff.name;

                if (diffKey === this.currentDifficulty) {
                    button.classList.add('active');
                }

                buttonContainer.appendChild(button);
            });
        }

        difficultySelector.appendChild(buttonContainer);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 模式切换事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mode-btn')) {
                const mode = e.target.dataset.mode;
                this.switchMode(mode);
            }
            
            if (e.target.classList.contains('difficulty-btn')) {
                const difficulty = e.target.dataset.difficulty;
                this.switchDifficulty(difficulty);
            }
        });
    }
    
    /**
     * 切换练习模式
     * @param {string} mode - 模式名称
     */
    switchMode(mode) {
        if (this.modes[mode]) {
            this.currentMode = mode;
            this.currentDifficulty = 'beginner'; // 重置为初级难度
            
            // 更新UI
            this.updateModeButtons();
            this.createDifficultySelector();
            
            // 生成新的练习文本
            this.generatePracticeText();
            
            console.log(`切换到模式: ${mode}`);
        }
    }
    
    /**
     * 切换难度等级
     * @param {string} difficulty - 难度等级
     */
    switchDifficulty(difficulty) {
        const currentModeConfig = this.modes[this.currentMode];
        if (currentModeConfig && currentModeConfig.difficulties[difficulty]) {
            this.currentDifficulty = difficulty;
            
            // 更新UI
            this.updateDifficultyButtons();
            
            // 生成新的练习文本
            this.generatePracticeText();
            
            console.log(`切换到难度: ${difficulty}`);
        }
    }
    
    /**
     * 更新模式按钮状态
     */
    updateModeButtons() {
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.mode === this.currentMode) {
                btn.classList.add('active');
            }
        });
    }
    
    /**
     * 更新难度按钮状态
     */
    updateDifficultyButtons() {
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.difficulty === this.currentDifficulty) {
                btn.classList.add('active');
            }
        });
    }
    
    /**
     * 生成练习文本
     * @returns {string} 生成的练习文本
     */
    generatePracticeText() {
        const modeConfig = this.modes[this.currentMode];
        const difficultyConfig = modeConfig.difficulties[this.currentDifficulty];
        
        let text = '';
        
        switch (this.currentMode) {
            case 'words':
                text = this.generateWordsText(difficultyConfig);
                break;
            case 'sentences':
                text = this.generateSentencesText(difficultyConfig);
                break;
            case 'paragraphs':
                text = this.generateParagraphsText(difficultyConfig);
                break;
            case 'numbers':
                text = this.generateNumbersText(difficultyConfig);
                break;
            case 'symbols':
                text = this.generateSymbolsText(difficultyConfig);
                break;
            case 'programming':
                text = this.generateProgrammingText(difficultyConfig);
                break;
            case 'speed':
                text = this.generateSpeedText(difficultyConfig);
                break;
            case 'accuracy':
                text = this.generateAccuracyText(difficultyConfig);
                break;
            default:
                text = this.generateWordsText(difficultyConfig);
        }
        
        // 通知打字管理器更新文本
        if (window.typingManager) {
            typingManager.state.currentText = text;
            typingManager.displayText();
            
            // 更新统计管理器
            if (window.statsManager) {
                statsManager.setTotalCharacters(text.length);
            }
        }
        
        return text;
    }
    
    /**
     * 生成单词练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateWordsText(config) {
        const words = window.typingManager?.textData?.words || [
            'the', 'quick', 'brown', 'fox', 'jumps', 'over', 'lazy', 'dog',
            'hello', 'world', 'javascript', 'programming', 'computer', 'keyboard'
        ];
        
        const selectedWords = this.getRandomItems(words, config.wordCount);
        return selectedWords.join(' ');
    }
    
    /**
     * 生成句子练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateSentencesText(config) {
        const sentences = window.typingManager?.textData?.sentences || [
            'The quick brown fox jumps over the lazy dog.',
            'JavaScript is a versatile programming language.',
            'Practice makes perfect in typing skills.'
        ];
        
        const selectedSentences = this.getRandomItems(sentences, config.sentenceCount);
        return selectedSentences.join(' ');
    }
    
    /**
     * 生成段落练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateParagraphsText(config) {
        const paragraphs = window.typingManager?.textData?.paragraphs || [
            'JavaScript is a high-level programming language. It is widely used for web development.'
        ];
        
        let text = this.getRandomItems(paragraphs, 1)[0];
        
        // 根据长度配置调整文本
        switch (config.length) {
            case 'short':
                text = text.substring(0, 150);
                break;
            case 'medium':
                text = text.substring(0, 300);
                break;
            case 'long':
                text = text.substring(0, 500);
                break;
            case 'very_long':
                // 使用完整文本或组合多个段落
                break;
        }
        
        return text;
    }
    
    /**
     * 生成数字练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateNumbersText(config) {
        let numbers = [];
        
        for (let i = 0; i < config.length; i++) {
            switch (config.pattern) {
                case 'simple':
                    numbers.push(Math.floor(Math.random() * 10).toString());
                    break;
                case 'mixed':
                    numbers.push(Math.floor(Math.random() * 100).toString());
                    break;
                case 'complex':
                    numbers.push(Math.floor(Math.random() * 10000).toString());
                    break;
                case 'extreme':
                    numbers.push((Math.random() * 1000000).toFixed(2));
                    break;
            }
        }
        
        return numbers.join(' ');
    }
    
    /**
     * 生成符号练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateSymbolsText(config) {
        const symbols = [];
        const words = ['hello', 'world', 'test', 'code', 'type', 'fast', 'good', 'nice'];
        
        for (let i = 0; i < config.length; i++) {
            if (i % 3 === 0) {
                // 添加符号
                const symbol = config.symbols[Math.floor(Math.random() * config.symbols.length)];
                symbols.push(symbol);
            } else {
                // 添加单词
                const word = words[Math.floor(Math.random() * words.length)];
                symbols.push(word);
            }
        }
        
        return symbols.join(' ');
    }
    
    /**
     * 生成编程练习文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateProgrammingText(config) {
        const snippets = this.codeSnippets[config.language];
        if (snippets && snippets[config.complexity]) {
            const selectedSnippets = this.getRandomItems(snippets[config.complexity], 1);
            return selectedSnippets[0];
        }
        
        // 默认返回简单的代码
        return 'function hello() {\n  console.log("Hello, World!");\n}';
    }
    
    /**
     * 生成速度挑战文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateSpeedText(config) {
        // 使用常用单词生成适合速度练习的文本
        const commonWords = [
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy'
        ];
        
        const wordCount = Math.ceil(config.targetWPM * config.timeLimit / 60 * 5); // 估算单词数
        const selectedWords = this.getRandomItems(commonWords, wordCount);
        return selectedWords.join(' ');
    }
    
    /**
     * 生成准确率挑战文本
     * @param {Object} config - 难度配置
     * @returns {string} 练习文本
     */
    generateAccuracyText(config) {
        // 使用容易出错的字符组合
        const trickyWords = [
            'receive', 'achieve', 'believe', 'ceiling', 'weird', 'seize',
            'their', 'there', 'they\'re', 'your', 'you\'re', 'its', 'it\'s',
            'affect', 'effect', 'accept', 'except', 'advice', 'advise'
        ];
        
        const selectedWords = this.getRandomItems(trickyWords, 15);
        return selectedWords.join(' ');
    }
    
    /**
     * 从数组中随机选择项目
     * @param {Array} array - 源数组
     * @param {number} count - 选择数量
     * @returns {Array} 随机选择的项目
     */
    getRandomItems(array, count) {
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, Math.min(count, array.length));
    }
    
    /**
     * 获取当前模式信息
     * @returns {Object} 当前模式信息
     */
    getCurrentModeInfo() {
        const modeConfig = this.modes[this.currentMode];
        const difficultyConfig = modeConfig.difficulties[this.currentDifficulty];
        
        return {
            mode: this.currentMode,
            modeName: modeConfig.name,
            difficulty: this.currentDifficulty,
            difficultyName: difficultyConfig.name,
            config: difficultyConfig
        };
    }
    
    /**
     * 获取所有可用模式
     * @returns {Object} 所有模式配置
     */
    getAllModes() {
        return this.modes;
    }
}

// 创建全局练习模式管理器实例
const practiceModeManager = new PracticeModeManager();
