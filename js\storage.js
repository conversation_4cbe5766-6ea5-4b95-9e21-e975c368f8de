/**
 * 数据存储模块
 * 负责处理本地存储、用户设置和历史记录
 */

class StorageManager {
    constructor() {
        // 存储键名常量
        this.KEYS = {
            SETTINGS: 'typing_practice_settings',
            HISTORY: 'typing_practice_history',
            THEME: 'typing_practice_theme',
            STATS: 'typing_practice_stats'
        };
        
        // 默认设置
        this.defaultSettings = {
            showKeyboard: false,
            soundEnabled: true,
            textSize: 'medium',
            autoStart: false,
            highlightErrors: true,
            showProgress: true
        };
        
        // 初始化存储
        this.init();
    }
    
    /**
     * 初始化存储管理器
     * 检查浏览器支持并设置默认值
     */
    init() {
        // 检查 localStorage 支持
        if (!this.isLocalStorageSupported()) {
            console.warn('LocalStorage 不受支持，将使用内存存储');
            this.useMemoryStorage = true;
            this.memoryStorage = {};
        }
        
        // 初始化默认设置
        if (!this.getSettings()) {
            this.saveSettings(this.defaultSettings);
        }
        
        // 初始化历史记录数组
        if (!this.getHistory()) {
            this.saveHistory([]);
        }
        
        // 初始化统计数据
        if (!this.getStats()) {
            this.saveStats(this.getDefaultStats());
        }
    }
    
    /**
     * 检查 localStorage 是否受支持
     * @returns {boolean} 是否支持 localStorage
     */
    isLocalStorageSupported() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * 通用的存储方法
     * @param {string} key - 存储键
     * @param {*} value - 要存储的值
     */
    setItem(key, value) {
        try {
            if (this.useMemoryStorage) {
                this.memoryStorage[key] = JSON.stringify(value);
            } else {
                localStorage.setItem(key, JSON.stringify(value));
            }
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    }
    
    /**
     * 通用的获取方法
     * @param {string} key - 存储键
     * @returns {*} 存储的值，如果不存在则返回 null
     */
    getItem(key) {
        try {
            let item;
            if (this.useMemoryStorage) {
                item = this.memoryStorage[key];
            } else {
                item = localStorage.getItem(key);
            }
            
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('获取数据失败:', error);
            return null;
        }
    }
    
    /**
     * 删除存储项
     * @param {string} key - 要删除的键
     */
    removeItem(key) {
        try {
            if (this.useMemoryStorage) {
                delete this.memoryStorage[key];
            } else {
                localStorage.removeItem(key);
            }
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    }
    
    /**
     * 保存用户设置
     * @param {Object} settings - 设置对象
     */
    saveSettings(settings) {
        const currentSettings = this.getSettings() || {};
        const mergedSettings = { ...currentSettings, ...settings };
        this.setItem(this.KEYS.SETTINGS, mergedSettings);
    }
    
    /**
     * 获取用户设置
     * @returns {Object} 设置对象
     */
    getSettings() {
        return this.getItem(this.KEYS.SETTINGS);
    }
    
    /**
     * 获取单个设置值
     * @param {string} key - 设置键
     * @returns {*} 设置值
     */
    getSetting(key) {
        const settings = this.getSettings();
        return settings ? settings[key] : this.defaultSettings[key];
    }
    
    /**
     * 保存主题设置
     * @param {string} theme - 主题名称 ('light' 或 'dark')
     */
    saveTheme(theme) {
        this.setItem(this.KEYS.THEME, theme);
    }
    
    /**
     * 获取主题设置
     * @returns {string} 主题名称
     */
    getTheme() {
        return this.getItem(this.KEYS.THEME) || 'light';
    }
    
    /**
     * 保存练习历史记录
     * @param {Array} history - 历史记录数组
     */
    saveHistory(history) {
        // 限制历史记录数量，避免存储过多数据
        const maxHistory = 100;
        const limitedHistory = history.slice(-maxHistory);
        this.setItem(this.KEYS.HISTORY, limitedHistory);
    }
    
    /**
     * 获取练习历史记录
     * @returns {Array} 历史记录数组
     */
    getHistory() {
        return this.getItem(this.KEYS.HISTORY) || [];
    }
    
    /**
     * 添加新的练习记录
     * @param {Object} record - 练习记录对象
     */
    addHistoryRecord(record) {
        const history = this.getHistory();
        const newRecord = {
            ...record,
            timestamp: Date.now(),
            id: this.generateId()
        };
        history.push(newRecord);
        this.saveHistory(history);
        
        // 同时更新统计数据
        this.updateStats(record);
    }
    
    /**
     * 获取默认统计数据结构
     * @returns {Object} 默认统计数据
     */
    getDefaultStats() {
        return {
            totalPractices: 0,
            totalTime: 0,
            totalCharacters: 0,
            totalErrors: 0,
            bestWPM: 0,
            bestAccuracy: 0,
            averageWPM: 0,
            averageAccuracy: 0,
            lastPractice: null,
            streakDays: 0,
            totalDays: 0
        };
    }
    
    /**
     * 保存统计数据
     * @param {Object} stats - 统计数据对象
     */
    saveStats(stats) {
        this.setItem(this.KEYS.STATS, stats);
    }
    
    /**
     * 获取统计数据
     * @returns {Object} 统计数据对象
     */
    getStats() {
        return this.getItem(this.KEYS.STATS) || this.getDefaultStats();
    }
    
    /**
     * 更新统计数据
     * @param {Object} record - 新的练习记录
     */
    updateStats(record) {
        const stats = this.getStats();
        
        // 更新基础统计
        stats.totalPractices += 1;
        stats.totalTime += record.time;
        stats.totalCharacters += record.totalCharacters;
        stats.totalErrors += record.errors;
        
        // 更新最佳记录
        if (record.wpm > stats.bestWPM) {
            stats.bestWPM = record.wpm;
        }
        if (record.accuracy > stats.bestAccuracy) {
            stats.bestAccuracy = record.accuracy;
        }
        
        // 计算平均值
        stats.averageWPM = Math.round(
            (stats.averageWPM * (stats.totalPractices - 1) + record.wpm) / stats.totalPractices
        );
        stats.averageAccuracy = Math.round(
            (stats.averageAccuracy * (stats.totalPractices - 1) + record.accuracy) / stats.totalPractices
        );
        
        // 更新练习日期相关统计
        const today = new Date().toDateString();
        const lastPracticeDate = stats.lastPractice ? new Date(stats.lastPractice).toDateString() : null;
        
        if (lastPracticeDate !== today) {
            stats.totalDays += 1;
            
            // 计算连续练习天数
            if (lastPracticeDate) {
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                const yesterdayString = yesterday.toDateString();
                
                if (lastPracticeDate === yesterdayString) {
                    stats.streakDays += 1;
                } else {
                    stats.streakDays = 1;
                }
            } else {
                stats.streakDays = 1;
            }
        }
        
        stats.lastPractice = Date.now();
        this.saveStats(stats);
    }
    
    /**
     * 生成唯一ID
     * @returns {string} 唯一ID字符串
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    /**
     * 清除所有数据
     */
    clearAllData() {
        Object.values(this.KEYS).forEach(key => {
            this.removeItem(key);
        });
        
        if (this.useMemoryStorage) {
            this.memoryStorage = {};
        }
        
        // 重新初始化默认数据
        this.init();
    }
    
    /**
     * 导出数据
     * @returns {Object} 包含所有数据的对象
     */
    exportData() {
        return {
            settings: this.getSettings(),
            theme: this.getTheme(),
            history: this.getHistory(),
            stats: this.getStats(),
            exportDate: Date.now()
        };
    }
    
    /**
     * 导入数据
     * @param {Object} data - 要导入的数据对象
     * @returns {boolean} 导入是否成功
     */
    importData(data) {
        try {
            if (data.settings) this.saveSettings(data.settings);
            if (data.theme) this.saveTheme(data.theme);
            if (data.history) this.saveHistory(data.history);
            if (data.stats) this.saveStats(data.stats);
            
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }
}

// 创建全局存储管理器实例
const storageManager = new StorageManager();
