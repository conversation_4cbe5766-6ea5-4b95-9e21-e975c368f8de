/* 
 * 响应式样式文件
 * 针对不同屏幕尺寸的样式调整
 */

/* 大屏幕 (桌面) - 默认样式已在 style.css 中定义 */
@media (min-width: 1200px) {
    .container {
        padding: var(--spacing-xl);
    }
    
    .title {
        font-size: 3rem;
    }
    
    .text-display {
        font-size: 1.375rem;
        padding: var(--spacing-xl) 3rem;
        min-height: 250px;
    }
    
    .typing-input {
        font-size: 1.25rem;
        min-height: 140px;
    }
}

/* 中等屏幕 (平板横屏) */
@media (max-width: 1199px) and (min-width: 992px) {
    .container {
        padding: var(--spacing-lg);
    }
    
    .title {
        font-size: 2.25rem;
    }
    
    .stats-panel {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .text-display {
        font-size: 1.125rem;
        padding: var(--spacing-lg) var(--spacing-xl);
    }
}

/* 平板竖屏 */
@media (max-width: 991px) and (min-width: 768px) {
    .container {
        padding: var(--spacing-md);
    }
    
    .title {
        font-size: 2rem;
    }
    
    .header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .header-controls {
        justify-content: center;
    }
    
    .stats-panel {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }
    
    .stat-value {
        font-size: 1.75rem;
    }
    
    .mode-selector {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .mode-btn {
        flex: 1;
        min-width: 120px;
    }
    
    .text-display {
        font-size: 1rem;
        padding: var(--spacing-lg);
        min-height: 180px;
    }
    
    .virtual-keyboard {
        padding: var(--spacing-md);
    }

    .key {
        min-width: 2.5rem;
        height: 2.5rem;
        font-size: 0.875rem;
    }
    
    .controls {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .btn {
        flex: 1;
        min-width: 120px;
    }
}

/* 手机横屏 */
@media (max-width: 767px) and (min-width: 576px) {
    .container {
        padding: var(--spacing-sm);
    }
    
    .title {
        font-size: 1.75rem;
    }
    
    .header {
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
    }
    
    .stats-panel {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }
    
    .stat-item {
        padding: var(--spacing-sm);
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .stat-unit {
        font-size: 0.625rem;
    }
    
    .mode-selector {
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
    }
    
    .mode-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.875rem;
    }
    
    .text-display {
        font-size: 0.875rem;
        padding: var(--spacing-md);
        min-height: 150px;
        line-height: 1.6;
    }
    
    .virtual-keyboard {
        padding: var(--spacing-sm);
    }

    .key {
        min-width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .key-space {
        min-width: 10rem;
    }

    .key-modifier, .key-special {
        min-width: 3rem;
        font-size: 0.625rem;
    }
    
    .progress-container {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .progress-text {
        text-align: center;
        min-width: auto;
    }
}

/* 手机竖屏 (小屏幕) */
@media (max-width: 575px) {
    .container {
        padding: var(--spacing-sm);
        min-height: 100vh;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .header {
        flex-direction: column;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
    }
    
    .header-controls {
        gap: var(--spacing-xs);
    }
    
    .btn-icon {
        width: 2rem;
        height: 2rem;
    }
    
    .stats-panel {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .stat-item {
        padding: var(--spacing-xs);
    }
    
    .stat-label {
        font-size: 0.625rem;
        margin-bottom: 2px;
    }
    
    .stat-value {
        font-size: 1.125rem;
    }
    
    .stat-unit {
        font-size: 0.5rem;
    }
    
    .mode-selector {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs);
        margin-bottom: var(--spacing-md);
    }
    
    .mode-btn {
        width: 100%;
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }
    
    .practice-area {
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .text-display {
        font-size: 0.75rem;
        padding: var(--spacing-sm);
        min-height: 120px;
        line-height: 1.5;
    }
    
    .virtual-keyboard {
        padding: var(--spacing-xs);
        margin-top: var(--spacing-sm);
    }

    .keyboard-title {
        font-size: 1rem;
        margin-bottom: var(--spacing-sm);
    }

    .key {
        min-width: 1.75rem;
        height: 1.75rem;
        font-size: 0.625rem;
        padding: var(--spacing-xs);
    }

    .key-space {
        min-width: 8rem;
    }

    .key-modifier, .key-special {
        min-width: 2.5rem;
        font-size: 0.5rem;
    }

    .finger-guide {
        padding: var(--spacing-sm);
        margin-top: var(--spacing-sm);
    }

    .finger-color {
        min-width: 3rem;
        padding: var(--spacing-xs);
        font-size: 0.625rem;
    }
    
    .progress-container {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .progress-bar {
        height: 6px;
    }
    
    .progress-text {
        text-align: center;
        font-size: 0.875rem;
    }
    
    .controls {
        flex-direction: column;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-md);
    }
    
    .btn {
        width: 100%;
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }
    
    /* 模态框在小屏幕上的调整 */
    .modal-content {
        margin: var(--spacing-sm);
        width: calc(100% - 2 * var(--spacing-sm));
        max-height: calc(100vh - 2 * var(--spacing-sm));
        overflow-y: auto;
    }
    
    .modal-header {
        padding: var(--spacing-sm);
    }
    
    .modal-body {
        padding: var(--spacing-sm);
    }
    
    /* 结果面板在小屏幕上的调整 */
    .results-panel {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm);
    }
    
    .results-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }
    
    .results-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .results-actions .btn {
        width: 100%;
    }
}

/* 超小屏幕 (旧手机) */
@media (max-width: 320px) {
    .container {
        padding: 0.25rem;
    }
    
    .title {
        font-size: 1.25rem;
    }
    
    .text-display {
        font-size: 0.625rem;
        padding: 0.25rem;
        min-height: 100px;
    }
    
    .typing-input {
        font-size: 0.625rem;
        min-height: 50px;
        padding: 0.25rem;
    }
    
    .stat-value {
        font-size: 1rem;
    }
}

/* 横屏模式特殊处理 */
@media (orientation: landscape) and (max-height: 500px) {
    .container {
        padding: var(--spacing-xs);
    }
    
    .header {
        margin-bottom: var(--spacing-sm);
        padding-bottom: var(--spacing-xs);
    }
    
    .title {
        font-size: 1.25rem;
    }
    
    .stats-panel {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-xs);
    }
    
    .mode-selector {
        margin-bottom: var(--spacing-sm);
    }
    
    .text-display {
        min-height: 80px;
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }
    
    .typing-input {
        min-height: 50px;
        font-size: 0.75rem;
    }
    
    .controls {
        margin-bottom: var(--spacing-sm);
    }
}

/* 打印样式 */
@media print {
    .header-controls,
    .controls,
    .virtual-keyboard,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .text-display,
    .typing-input {
        border: 1px solid #000;
        background: white !important;
        color: black !important;
    }
}
