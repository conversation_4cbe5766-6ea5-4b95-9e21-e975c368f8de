/**
 * 虚拟键盘模块
 * 负责显示虚拟键盘、高亮当前按键和手指位置指导
 */

class VirtualKeyboard {
    constructor() {
        // 键盘布局配置
        this.layouts = {
            qwerty: {
                rows: [
                    ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'Backspace'],
                    ['Tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
                    ['CapsLock', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'Enter'],
                    ['Shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'Shift'],
                    ['Ctrl', 'Win', 'Alt', 'Space', 'Alt', 'Win', 'Menu', 'Ctrl']
                ],
                fingerMap: {
                    // 左手小指
                    '`': 'left-pinky', '1': 'left-pinky', 'q': 'left-pinky', 'a': 'left-pinky', 'z': 'left-pinky',
                    'Tab': 'left-pinky', 'CapsLock': 'left-pinky', 'Shift': 'left-pinky',
                    
                    // 左手无名指
                    '2': 'left-ring', 'w': 'left-ring', 's': 'left-ring', 'x': 'left-ring',
                    
                    // 左手中指
                    '3': 'left-middle', 'e': 'left-middle', 'd': 'left-middle', 'c': 'left-middle',
                    
                    // 左手食指
                    '4': 'left-index', '5': 'left-index', 'r': 'left-index', 't': 'left-index',
                    'f': 'left-index', 'g': 'left-index', 'v': 'left-index', 'b': 'left-index',
                    
                    // 右手食指
                    '6': 'right-index', '7': 'right-index', 'y': 'right-index', 'u': 'right-index',
                    'h': 'right-index', 'j': 'right-index', 'n': 'right-index', 'm': 'right-index',
                    
                    // 右手中指
                    '8': 'right-middle', 'i': 'right-middle', 'k': 'right-middle', ',': 'right-middle',
                    
                    // 右手无名指
                    '9': 'right-ring', 'o': 'right-ring', 'l': 'right-ring', '.': 'right-ring',
                    
                    // 右手小指
                    '0': 'right-pinky', '-': 'right-pinky', '=': 'right-pinky', 'Backspace': 'right-pinky',
                    'p': 'right-pinky', '[': 'right-pinky', ']': 'right-pinky', '\\': 'right-pinky',
                    ';': 'right-pinky', "'": 'right-pinky', 'Enter': 'right-pinky',
                    '/': 'right-pinky',
                    
                    // 拇指
                    'Space': 'thumb', 'Alt': 'thumb', 'Ctrl': 'thumb', 'Win': 'thumb', 'Menu': 'thumb'
                }
            }
        };
        
        // 当前状态
        this.state = {
            isVisible: false,
            currentLayout: 'qwerty',
            activeKey: null,
            activeFinger: null,
            isShiftPressed: false,
            isCapsLockOn: false
        };
        
        // DOM元素
        this.elements = {
            container: null,
            keys: new Map()
        };
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化虚拟键盘
     */
    init() {
        this.createKeyboard();
        this.bindEvents();
        
        // 根据设置决定是否显示
        if (window.storageManager) {
            const showKeyboard = storageManager.getSetting('showKeyboard');
            if (showKeyboard) {
                this.show();
            }
        }
        
        console.log('虚拟键盘初始化完成');
    }
    
    /**
     * 创建键盘DOM结构
     */
    createKeyboard() {
        const container = document.getElementById('virtual-keyboard');
        if (!container) {
            console.error('找不到虚拟键盘容器');
            return;
        }
        
        this.elements.container = container;
        
        // 清空容器
        container.innerHTML = '';
        
        // 添加键盘标题
        const title = document.createElement('div');
        title.className = 'keyboard-title';
        title.textContent = '虚拟键盘';
        container.appendChild(title);
        
        // 创建键盘行
        const layout = this.layouts[this.state.currentLayout];
        layout.rows.forEach((row, rowIndex) => {
            const rowElement = document.createElement('div');
            rowElement.className = `keyboard-row row-${rowIndex}`;
            
            row.forEach(keyText => {
                const keyElement = this.createKeyElement(keyText);
                rowElement.appendChild(keyElement);
                this.elements.keys.set(keyText.toLowerCase(), keyElement);
            });
            
            container.appendChild(rowElement);
        });
        
        // 添加手指指导
        this.createFingerGuide();
    }
    
    /**
     * 创建按键元素
     * @param {string} keyText - 按键文本
     * @returns {HTMLElement} 按键元素
     */
    createKeyElement(keyText) {
        const keyElement = document.createElement('div');
        keyElement.className = 'key';
        keyElement.dataset.key = keyText.toLowerCase();
        
        // 设置按键样式类
        if (keyText === 'Space') {
            keyElement.classList.add('key-space');
            keyElement.textContent = '';
        } else if (['Shift', 'Ctrl', 'Alt', 'Win', 'Menu'].includes(keyText)) {
            keyElement.classList.add('key-modifier');
            keyElement.textContent = keyText;
        } else if (['Tab', 'CapsLock', 'Enter', 'Backspace'].includes(keyText)) {
            keyElement.classList.add('key-special');
            keyElement.textContent = keyText;
        } else {
            keyElement.textContent = keyText;
        }
        
        // 添加手指指导类
        const finger = this.getFingerForKey(keyText);
        if (finger) {
            keyElement.classList.add(`finger-${finger}`);
        }
        
        // 添加点击事件
        keyElement.addEventListener('click', () => {
            this.handleKeyClick(keyText);
        });
        
        return keyElement;
    }
    
    /**
     * 创建手指指导
     */
    createFingerGuide() {
        const guide = document.createElement('div');
        guide.className = 'finger-guide';
        guide.innerHTML = `
            <div class="finger-guide-title">手指指导</div>
            <div class="finger-colors">
                <div class="finger-color left-pinky">左小指</div>
                <div class="finger-color left-ring">左无名指</div>
                <div class="finger-color left-middle">左中指</div>
                <div class="finger-color left-index">左食指</div>
                <div class="finger-color thumb">拇指</div>
                <div class="finger-color right-index">右食指</div>
                <div class="finger-color right-middle">右中指</div>
                <div class="finger-color right-ring">右无名指</div>
                <div class="finger-color right-pinky">右小指</div>
            </div>
        `;
        
        this.elements.container.appendChild(guide);
    }
    
    /**
     * 获取按键对应的手指
     * @param {string} key - 按键
     * @returns {string} 手指标识
     */
    getFingerForKey(key) {
        const layout = this.layouts[this.state.currentLayout];
        return layout.fingerMap[key.toLowerCase()] || layout.fingerMap[key];
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 监听真实键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });
        
        // 监听设置变化
        document.addEventListener('app:setting-changed', (e) => {
            if (e.detail.key === 'showKeyboard') {
                if (e.detail.value) {
                    this.show();
                } else {
                    this.hide();
                }
            }
        });
    }
    
    /**
     * 处理键盘按下事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyDown(e) {
        if (!this.state.isVisible) return;
        
        let key = e.key;
        
        // 处理特殊按键
        if (key === ' ') key = 'Space';
        if (key === 'Control') key = 'Ctrl';
        if (key === 'Meta') key = 'Win';
        
        // 更新修饰键状态
        if (key === 'Shift') {
            this.state.isShiftPressed = true;
        }
        if (key === 'CapsLock') {
            this.state.isCapsLockOn = !this.state.isCapsLockOn;
        }
        
        // 高亮按键
        this.highlightKey(key);
    }
    
    /**
     * 处理键盘释放事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyUp(e) {
        if (!this.state.isVisible) return;
        
        let key = e.key;
        
        // 处理特殊按键
        if (key === ' ') key = 'Space';
        if (key === 'Control') key = 'Ctrl';
        if (key === 'Meta') key = 'Win';
        
        // 更新修饰键状态
        if (key === 'Shift') {
            this.state.isShiftPressed = false;
        }
        
        // 取消高亮
        this.unhighlightKey(key);
    }
    
    /**
     * 处理虚拟按键点击
     * @param {string} key - 按键
     */
    handleKeyClick(key) {
        // 模拟键盘事件
        const event = new KeyboardEvent('keydown', {
            key: key === 'Space' ? ' ' : key,
            code: `Key${key.toUpperCase()}`,
            bubbles: true
        });
        
        document.dispatchEvent(event);
        
        // 短暂高亮
        this.highlightKey(key);
        setTimeout(() => {
            this.unhighlightKey(key);
        }, 150);
    }
    
    /**
     * 高亮按键
     * @param {string} key - 要高亮的按键
     */
    highlightKey(key) {
        const keyElement = this.elements.keys.get(key.toLowerCase());
        if (keyElement) {
            keyElement.classList.add('key-active');
            this.state.activeKey = key;

            // 根据设置决定是否显示手指指导
            const finger = this.getFingerForKey(key);
            if (finger) {
                const fingerGuideEnabled = window.storageManager ?
                    storageManager.getSetting('fingerGuide') : false;

                if (fingerGuideEnabled) {
                    this.highlightFinger(finger); // 显示所有相关按键
                } else {
                    this.highlightFingerGuide(finger); // 只显示指导面板
                }
            }
        }
    }
    
    /**
     * 取消按键高亮
     * @param {string} key - 要取消高亮的按键
     */
    unhighlightKey(key) {
        const keyElement = this.elements.keys.get(key.toLowerCase());
        if (keyElement) {
            keyElement.classList.remove('key-active');

            if (this.state.activeKey === key) {
                this.state.activeKey = null;

                // 根据设置决定取消哪种高亮
                const fingerGuideEnabled = window.storageManager ?
                    storageManager.getSetting('fingerGuide') : false;

                if (fingerGuideEnabled) {
                    this.unhighlightFinger(); // 取消所有按键高亮
                } else {
                    this.unhighlightFingerGuide(); // 只取消指导面板高亮
                }
            }
        }
    }
    
    /**
     * 高亮手指指导（只高亮指导面板，不高亮其他按键）
     * @param {string} finger - 手指标识
     */
    highlightFingerGuide(finger) {
        // 移除之前的高亮
        this.unhighlightFingerGuide();

        // 只高亮手指指导面板
        const fingerGuide = this.elements.container.querySelector(`.finger-color.${finger}`);
        if (fingerGuide) {
            fingerGuide.classList.add('finger-active');
        }

        this.state.activeFinger = finger;
    }

    /**
     * 取消手指指导高亮
     */
    unhighlightFingerGuide() {
        if (this.state.activeFinger) {
            // 移除手指指导高亮
            const fingerGuide = this.elements.container.querySelector(`.finger-color.${this.state.activeFinger}`);
            if (fingerGuide) {
                fingerGuide.classList.remove('finger-active');
            }

            this.state.activeFinger = null;
        }
    }

    /**
     * 高亮手指指导（显示该手指负责的所有按键 - 用于教学模式）
     * @param {string} finger - 手指标识
     */
    highlightFinger(finger) {
        // 移除之前的高亮
        this.unhighlightFinger();

        // 高亮对应手指的所有按键
        this.elements.keys.forEach(keyElement => {
            if (keyElement.classList.contains(`finger-${finger}`)) {
                keyElement.classList.add('finger-highlight');
            }
        });

        // 高亮手指指导
        const fingerGuide = this.elements.container.querySelector(`.finger-color.${finger}`);
        if (fingerGuide) {
            fingerGuide.classList.add('finger-active');
        }

        this.state.activeFinger = finger;
    }

    /**
     * 取消手指高亮（取消所有按键高亮）
     */
    unhighlightFinger() {
        if (this.state.activeFinger) {
            // 移除按键高亮
            this.elements.keys.forEach(keyElement => {
                keyElement.classList.remove('finger-highlight');
            });

            // 移除手指指导高亮
            const fingerGuide = this.elements.container.querySelector(`.finger-color.${this.state.activeFinger}`);
            if (fingerGuide) {
                fingerGuide.classList.remove('finger-active');
            }

            this.state.activeFinger = null;
        }
    }
    
    /**
     * 显示虚拟键盘
     */
    show() {
        if (this.elements.container) {
            this.elements.container.classList.remove('hidden');
            this.state.isVisible = true;
            console.log('虚拟键盘已显示');
        }
    }
    
    /**
     * 隐藏虚拟键盘
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('hidden');
            this.state.isVisible = false;
            console.log('虚拟键盘已隐藏');
        }
    }
    
    /**
     * 切换显示状态
     */
    toggle() {
        if (this.state.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    /**
     * 获取当前状态
     * @returns {Object} 当前状态
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * 销毁虚拟键盘
     */
    destroy() {
        if (this.elements.container) {
            this.elements.container.innerHTML = '';
        }
        this.elements.keys.clear();
        console.log('虚拟键盘已销毁');
    }
}

// 创建全局虚拟键盘实例
const virtualKeyboard = new VirtualKeyboard();
