/* 
 * 打字练习应用 - 主样式文件
 * 包含基础样式、布局和组件样式
 */

/* CSS变量定义 - 便于主题切换和维护 */
:root {
    /* 颜色变量 */
    --primary-color: #3b82f6;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    
    /* 文字颜色 */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    /* 边框和阴影 */
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-mono: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.title {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    flex: 1;
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* 按钮基础样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 按钮变体 */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: #cbd5e1;
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
}

/* 统计面板 */
.stats-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.stat-unit {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 模式选择器 */
.mode-selector {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-sm);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow-x: auto;
}

.mode-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    background-color: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.mode-btn:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.mode-btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* 练习区域 */
.practice-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 文本显示区域 */
.text-display {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    font-family: var(--font-mono);
    font-size: 1.25rem;
    line-height: 1.8;
    min-height: 200px;
    position: relative;
    overflow-wrap: break-word;
    user-select: none;
}

.loading {
    color: var(--text-muted);
    font-style: italic;
}

/* 文本状态样式 */
.char-correct {
    background-color: var(--success-color);
    color: white;
}

.char-incorrect {
    background-color: var(--error-color);
    color: white;
}

.char-current {
    background-color: var(--primary-color);
    color: white;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 输入区域 */
.input-area {
    position: relative;
}

.typing-input {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-family: var(--font-mono);
    font-size: 1.125rem;
    line-height: 1.6;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    resize: vertical;
    transition: border-color 0.2s ease;
}

.typing-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* 进度条 */
.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 3rem;
    text-align: right;
}

/* 控制按钮区域 */
.controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    padding: var(--spacing-lg);
}

.setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
}

.setting-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.setting-group input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
}

.setting-group select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
}

/* 结果面板样式 */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 90%;
    text-align: center;
    z-index: 1000;
    border: 2px solid var(--primary-color);
}

.results-panel h2 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--primary-color);
    font-size: 1.75rem;
}

.results-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.result-item {
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.result-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.result-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.results-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* 虚拟键盘样式 */
.virtual-keyboard {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.keyboard-row {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.key {
    padding: var(--spacing-sm);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    min-width: 2.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.key:hover {
    background-color: var(--bg-tertiary);
}

.key.active {
    background-color: var(--primary-color);
    color: white;
    transform: scale(0.95);
}

.key.space {
    min-width: 8rem;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--success-color);
}

.text-error {
    color: var(--error-color);
}

.text-warning {
    color: var(--warning-color);
}
