/* 
 * 打字练习应用 - 主样式文件
 * 包含基础样式、布局和组件样式
 */

/* CSS变量定义 - 便于主题切换和维护 */
:root {
    /* 颜色变量 */
    --primary-color: #3b82f6;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    
    /* 文字颜色 */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    /* 边框和阴影 */
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-mono: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
}

/* 基础重置和全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.title {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    flex: 1;
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* 按钮基础样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 按钮变体 */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: #cbd5e1;
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
}

/* 统计面板 */
.stats-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.stat-unit {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 模式选择器 */
.mode-selector {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    flex-wrap: wrap;
}

.mode-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background-color: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 0.875rem;
    position: relative;
}

.mode-btn:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.mode-btn.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* 难度选择器 */
.difficulty-selector {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.difficulty-title {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.difficulty-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 4rem;
}

.difficulty-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.difficulty-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* 模式信息提示 */
.mode-info {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    border-left: 3px solid var(--primary-color);
}

.mode-info-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-xs);
}

.mode-info-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
    line-height: 1.4;
}

/* 挑战模式特殊样式 */
.challenge-mode .mode-btn {
    background: linear-gradient(45deg, var(--primary-color), var(--success-color));
    color: white;
    font-weight: 600;
}

.challenge-mode .mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 专项练习模式样式 */
.specialty-mode .mode-btn {
    border: 2px solid var(--warning-color);
    color: var(--warning-color);
}

.specialty-mode .mode-btn.active {
    background-color: var(--warning-color);
    color: white;
}

/* 编程模式特殊样式 */
.programming-mode .text-display {
    font-family: var(--font-mono);
    background-color: #1e1e1e;
    color: #d4d4d4;
    white-space: pre-wrap;
    tab-size: 2;
}

.programming-mode .char-correct {
    background-color: #4caf50;
}

.programming-mode .char-incorrect {
    background-color: #f44336;
}

.programming-mode .char-current {
    background-color: #2196f3;
}

/* 练习区域 */
.practice-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* 文本显示区域 */
.text-display {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    font-family: var(--font-mono);
    font-size: 1.25rem;
    line-height: 1.8;
    min-height: 200px;
    position: relative;
    overflow-wrap: break-word;
    user-select: none;
}

.loading {
    color: var(--text-muted);
    font-style: italic;
}

/* 文本状态样式 */
.char-correct {
    background-color: var(--success-color);
    color: white;
}

.char-incorrect {
    background-color: var(--error-color);
    color: white;
}

.char-current {
    background-color: var(--primary-color);
    color: white;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 输入提示区域 */
.input-hint {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.input-hint p {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 500;
}

.hint-text {
    margin-top: var(--spacing-sm) !important;
    color: var(--text-secondary) !important;
    font-size: 0.875rem !important;
    font-weight: 400 !important;
}

/* 进度条 */
.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 3rem;
    text-align: right;
}

/* 控制按钮区域 */
.controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    padding: var(--spacing-lg);
}

.setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
}

.setting-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.setting-group input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
}

.setting-group select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
}

/* 结果面板样式 */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 90%;
    text-align: center;
    z-index: 1000;
    border: 2px solid var(--primary-color);
}

.results-panel h2 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--primary-color);
    font-size: 1.75rem;
}

.results-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.result-item {
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.result-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.result-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

.results-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* 虚拟键盘样式 */
.virtual-keyboard {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.keyboard-title {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
}

.keyboard-row {
    display: flex;
    justify-content: center;
    gap: 6px;
    margin-bottom: 6px;
}

.keyboard-row:last-of-type {
    margin-bottom: var(--spacing-lg);
}

.key {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 1rem;
    font-weight: 500;
    min-width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.key:hover {
    background-color: var(--bg-tertiary);
    transform: translateY(-1px);
}

.key-active {
    background-color: var(--primary-color) !important;
    color: white !important;
    transform: scale(0.95) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 特殊按键样式 */
.key-space {
    min-width: 18rem;
}

.key-modifier {
    min-width: 5rem;
    font-size: 0.875rem;
    background-color: var(--bg-tertiary);
}

.key-special {
    min-width: 6rem;
    font-size: 0.875rem;
    background-color: var(--bg-tertiary);
}

/* 手指颜色指导 */
.finger-left-pinky { border-left: 4px solid #ff6b6b; }
.finger-left-ring { border-left: 4px solid #4ecdc4; }
.finger-left-middle { border-left: 4px solid #45b7d1; }
.finger-left-index { border-left: 4px solid #96ceb4; }
.finger-thumb { border-bottom: 4px solid #feca57; }
.finger-right-index { border-right: 4px solid #96ceb4; }
.finger-right-middle { border-right: 4px solid #45b7d1; }
.finger-right-ring { border-right: 4px solid #4ecdc4; }
.finger-right-pinky { border-right: 4px solid #ff6b6b; }

/* 手指高亮效果 */
.finger-highlight {
    background-color: var(--warning-color) !important;
    color: white !important;
    animation: fingerPulse 1s infinite;
}

@keyframes fingerPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 手指指导面板 */
.finger-guide {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.finger-guide-title {
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
}

.finger-colors {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.finger-color {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    text-align: center;
    min-width: 5rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.finger-color.left-pinky { background-color: #ff6b6b; }
.finger-color.left-ring { background-color: #4ecdc4; }
.finger-color.left-middle { background-color: #45b7d1; }
.finger-color.left-index { background-color: #96ceb4; }
.finger-color.thumb { background-color: #feca57; }
.finger-color.right-index { background-color: #96ceb4; }
.finger-color.right-middle { background-color: #45b7d1; }
.finger-color.right-ring { background-color: #4ecdc4; }
.finger-color.right-pinky { background-color: #ff6b6b; }

.finger-color.finger-active {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    animation: fingerGlow 0.5s ease-in-out;
}

@keyframes fingerGlow {
    0% { box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 0, 0, 0.5); }
    100% { box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); }
}

/* 分析面板样式 */
.analytics-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border-bottom: 1px solid var(--border-color);
}

.analytics-header h2 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.analytics-content {
    background-color: var(--bg-primary);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.analytics-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.analytics-section:last-child {
    border-bottom: none;
}

.analytics-section h3 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

/* 总览统计 */
.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.overview-item {
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.overview-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.overview-value {
    display: block;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-mono);
}

/* 图表容器 */
.chart-container {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.chart-container canvas {
    width: 100%;
    height: auto;
    max-height: 300px;
}

/* 错误分析 */
.error-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.error-chart {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.error-details {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.error-details h4 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.error-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.error-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background-color: var(--bg-primary);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--error-color);
}

.error-char {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-primary);
}

.error-count {
    font-size: 0.875rem;
    color: var(--error-color);
    font-weight: 500;
}

/* 手指分析 */
.finger-analysis {
    margin-top: var(--spacing-md);
}

.finger-recommendations {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.finger-recommendation {
    padding: var(--spacing-sm);
    background-color: var(--warning-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
}

/* 时间分析 */
.time-analysis {
    margin-top: var(--spacing-md);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

/* 学习建议 */
.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.suggestion {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.suggestion h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1rem;
}

.suggestion p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.suggestion-success {
    background-color: rgba(16, 185, 129, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.suggestion-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-left-color: var(--warning-color);
    color: var(--warning-color);
}

.suggestion-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.no-data {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: var(--spacing-lg);
}

/* 工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--success-color);
}

.text-error {
    color: var(--error-color);
}

.text-warning {
    color: var(--warning-color);
}
