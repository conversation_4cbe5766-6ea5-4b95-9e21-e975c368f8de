/**
 * 主应用文件
 * 负责应用初始化、主题管理和全局功能协调
 */

class App {
    constructor() {
        // 应用状态
        this.state = {
            isInitialized: false,
            currentTheme: 'light',
            settings: {}
        };
        
        // DOM元素引用
        this.elements = {
            themeToggle: document.getElementById('theme-toggle'),
            settingsBtn: document.getElementById('settings-btn'),
            analyticsBtn: document.getElementById('analytics-btn'),
            settingsModal: document.getElementById('settings-modal'),
            closeSettings: document.getElementById('close-settings')
        };
        
        // 初始化应用
        this.init();
    }
    
    /**
     * 初始化应用
     */
    async init() {
        try {
            console.log('应用初始化开始...');
            
            // 加载用户设置
            await this.loadSettings();
            
            // 初始化主题
            this.initTheme();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 初始化设置面板
            this.initSettingsPanel();
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 标记为已初始化
            this.state.isInitialized = true;
            
            console.log('应用初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('app:initialized');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }
    }
    
    /**
     * 加载用户设置
     */
    async loadSettings() {
        try {
            // 从存储管理器加载设置
            if (window.storageManager) {
                this.state.settings = storageManager.getSettings() || {};
                this.state.currentTheme = storageManager.getTheme() || 'light';
            }
            
            console.log('设置加载完成:', this.state.settings);
        } catch (error) {
            console.error('加载设置失败:', error);
            // 使用默认设置
            this.state.settings = {
                showKeyboard: false,
                soundEnabled: true,
                textSize: 'medium'
            };
        }
    }
    
    /**
     * 初始化主题
     */
    initTheme() {
        // 检查系统主题偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // 如果没有保存的主题设置，使用系统偏好
        if (!storageManager.getTheme()) {
            this.state.currentTheme = prefersDark ? 'dark' : 'light';
        }
        
        // 应用主题
        this.applyTheme(this.state.currentTheme);
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!storageManager.getTheme()) {
                this.applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
    
    /**
     * 应用主题
     * @param {string} theme - 主题名称 ('light' 或 'dark')
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.state.currentTheme = theme;
        
        // 更新主题切换按钮
        if (this.elements.themeToggle) {
            const icon = this.elements.themeToggle.querySelector('.theme-icon');
            if (icon) {
                icon.textContent = theme === 'dark' ? '☀️' : '🌙';
            }
        }
        
        // 保存主题设置
        if (window.storageManager) {
            storageManager.saveTheme(theme);
        }
        
        console.log(`主题已切换到: ${theme}`);
    }
    
    /**
     * 切换主题
     */
    toggleTheme() {
        const newTheme = this.state.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 主题切换按钮
        if (this.elements.themeToggle) {
            this.elements.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
        
        // 设置按钮
        if (this.elements.settingsBtn) {
            this.elements.settingsBtn.addEventListener('click', () => {
                this.showSettings();
            });
        }

        // 统计分析按钮
        if (this.elements.analyticsBtn) {
            this.elements.analyticsBtn.addEventListener('click', () => {
                this.showAnalytics();
            });
        }
        
        // 关闭设置按钮
        if (this.elements.closeSettings) {
            this.elements.closeSettings.addEventListener('click', () => {
                this.hideSettings();
            });
        }
        
        // 点击模态框外部关闭
        if (this.elements.settingsModal) {
            this.elements.settingsModal.addEventListener('click', (e) => {
                if (e.target === this.elements.settingsModal) {
                    this.hideSettings();
                }
            });
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeyboard(e);
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 页面卸载前
        window.addEventListener('beforeunload', (e) => {
            this.handleBeforeUnload(e);
        });
    }
    
    /**
     * 初始化设置面板
     */
    initSettingsPanel() {
        // 显示虚拟键盘设置
        const showKeyboardCheckbox = document.getElementById('show-keyboard');
        if (showKeyboardCheckbox) {
            showKeyboardCheckbox.checked = this.state.settings.showKeyboard || false;
            showKeyboardCheckbox.addEventListener('change', (e) => {
                this.updateSetting('showKeyboard', e.target.checked);
            });
        }
        
        // 音效设置
        const soundEnabledCheckbox = document.getElementById('sound-enabled');
        if (soundEnabledCheckbox) {
            soundEnabledCheckbox.checked = this.state.settings.soundEnabled !== false;
            soundEnabledCheckbox.addEventListener('change', (e) => {
                this.updateSetting('soundEnabled', e.target.checked);
            });
        }
        
        // 文字大小设置
        const textSizeSelect = document.getElementById('text-size');
        if (textSizeSelect) {
            textSizeSelect.value = this.state.settings.textSize || 'medium';
            textSizeSelect.addEventListener('change', (e) => {
                this.updateSetting('textSize', e.target.value);
                this.applyTextSize(e.target.value);
            });
            
            // 应用当前文字大小
            this.applyTextSize(this.state.settings.textSize || 'medium');
        }
    }
    
    /**
     * 更新设置
     * @param {string} key - 设置键
     * @param {*} value - 设置值
     */
    updateSetting(key, value) {
        this.state.settings[key] = value;
        
        // 保存到存储
        if (window.storageManager) {
            storageManager.saveSettings({ [key]: value });
        }
        
        console.log(`设置已更新: ${key} = ${value}`);
        
        // 触发设置变化事件
        this.dispatchEvent('app:setting-changed', { key, value });
    }
    
    /**
     * 应用文字大小
     * @param {string} size - 文字大小 ('small', 'medium', 'large')
     */
    applyTextSize(size) {
        const root = document.documentElement;
        
        switch (size) {
            case 'small':
                root.style.setProperty('--text-scale', '0.875');
                break;
            case 'large':
                root.style.setProperty('--text-scale', '1.125');
                break;
            default: // medium
                root.style.setProperty('--text-scale', '1');
        }
    }
    
    /**
     * 显示设置面板
     */
    showSettings() {
        if (this.elements.settingsModal) {
            this.elements.settingsModal.classList.remove('hidden');
            
            // 聚焦到第一个可聚焦元素
            const firstFocusable = this.elements.settingsModal.querySelector('input, select, button');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }
    }
    
    /**
     * 隐藏设置面板
     */
    hideSettings() {
        if (this.elements.settingsModal) {
            this.elements.settingsModal.classList.add('hidden');
        }
    }

    /**
     * 显示统计分析面板
     */
    showAnalytics() {
        if (window.analyticsManager) {
            analyticsManager.showAnalytics();
        }
    }
    
    /**
     * 处理全局键盘事件
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleGlobalKeyboard(e) {
        // Ctrl/Cmd + 组合键
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case ',':
                    e.preventDefault();
                    this.showSettings();
                    break;
                case 'd':
                    e.preventDefault();
                    this.toggleTheme();
                    break;
            }
        }
        
        // Escape键关闭模态框
        if (e.key === 'Escape') {
            this.hideSettings();
        }
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 可以在这里添加响应式处理逻辑
        console.log('窗口大小已变化');
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停练习
            if (window.typingManager && typingManager.state.isActive && !typingManager.state.isPaused) {
                typingManager.togglePause();
            }
        }
    }
    
    /**
     * 处理页面卸载前事件
     * @param {BeforeUnloadEvent} e - 卸载事件
     */
    handleBeforeUnload(e) {
        // 如果正在练习，提示用户
        if (window.typingManager && typingManager.state.isActive && !typingManager.state.isCompleted) {
            e.preventDefault();
            e.returnValue = '您正在进行打字练习，确定要离开吗？';
            return e.returnValue;
        }
    }
    
    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const features = {
            localStorage: typeof Storage !== 'undefined',
            es6: typeof Symbol !== 'undefined',
            flexbox: CSS.supports('display', 'flex'),
            grid: CSS.supports('display', 'grid')
        };
        
        const unsupported = Object.entries(features)
            .filter(([, supported]) => !supported)
            .map(([feature]) => feature);
        
        if (unsupported.length > 0) {
            console.warn('不支持的浏览器特性:', unsupported);
            this.showWarning(`您的浏览器可能不完全支持此应用。不支持的特性: ${unsupported.join(', ')}`);
        }
    }
    
    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        console.error(message);
        alert(`错误: ${message}`);
    }
    
    /**
     * 显示警告信息
     * @param {string} message - 警告信息
     */
    showWarning(message) {
        console.warn(message);
        // 可以实现更友好的警告显示方式
    }
    
    /**
     * 触发自定义事件
     * @param {string} eventName - 事件名称
     * @param {*} detail - 事件详情
     */
    dispatchEvent(eventName, detail = null) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
    
    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return { ...this.state };
    }
}

// 等待DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// 导出App类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
}
