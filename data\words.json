{"common": ["the", "be", "to", "of", "and", "a", "in", "that", "have", "i", "it", "for", "not", "on", "with", "he", "as", "you", "do", "at", "this", "but", "his", "by", "from", "they", "she", "or", "an", "will", "my", "one", "all", "would", "there", "their", "what", "so", "up", "out", "if", "about", "who", "get", "which", "go", "me", "when", "make", "can", "like", "time", "no", "just", "him", "know", "take", "people", "into", "year", "your", "good", "some", "could", "them", "see", "other", "than", "then", "now", "look", "only", "come", "its", "over", "think", "also", "back", "after", "use", "two", "how", "our", "work", "first", "well", "way", "even", "new", "want", "because", "any", "these", "give", "day", "most", "us", "is", "was", "are"], "programming": ["function", "variable", "array", "object", "string", "number", "boolean", "null", "undefined", "class", "method", "property", "constructor", "prototype", "this", "return", "if", "else", "for", "while", "do", "switch", "case", "break", "continue", "try", "catch", "finally", "throw", "new", "delete", "typeof", "instanceof", "in", "const", "let", "var", "async", "await", "promise", "callback", "event", "listener", "element", "document", "window", "console", "log", "error", "warn", "info", "debug", "true", "false", "import", "export", "module", "require", "package", "library", "framework", "api", "http", "json", "xml", "html", "css", "javascript", "typescript", "node", "react", "vue", "angular", "express", "server", "client", "database", "query", "table", "row", "column", "index", "primary", "foreign", "key", "join", "select", "insert", "update", "delete", "create", "drop", "alter", "where", "order", "group", "having", "limit", "offset", "union", "inner", "outer", "left", "right"], "technology": ["computer", "software", "hardware", "internet", "website", "application", "program", "code", "data", "information", "system", "network", "server", "client", "browser", "mobile", "desktop", "laptop", "tablet", "smartphone", "operating", "windows", "linux", "macos", "android", "ios", "chrome", "firefox", "safari", "edge", "memory", "storage", "processor", "cpu", "gpu", "ram", "disk", "ssd", "hdd", "usb", "bluetooth", "wifi", "ethernet", "protocol", "tcp", "ip", "http", "https", "ftp", "ssh", "ssl", "tls", "encryption", "security", "password", "authentication", "authorization", "firewall", "virus", "malware", "backup", "cloud", "virtual", "machine", "container", "docker", "kubernetes", "git", "github", "gitlab", "bitbucket", "version", "control", "branch", "merge", "commit", "push", "pull", "clone", "fork", "issue", "bug", "feature"], "business": ["company", "business", "market", "customer", "client", "service", "product", "sales", "revenue", "profit", "cost", "price", "budget", "finance", "investment", "strategy", "plan", "goal", "target", "objective", "mission", "vision", "value", "brand", "marketing", "advertising", "promotion", "campaign", "social", "media", "content", "website", "online", "digital", "ecommerce", "retail", "wholesale", "supply", "chain", "logistics", "delivery", "shipping", "warehouse", "inventory", "stock", "order", "purchase", "payment", "invoice", "receipt", "contract", "agreement", "negotiation", "deal", "partnership", "collaboration", "team", "employee", "staff", "manager", "director", "executive", "ceo", "cto", "cfo", "hr", "human", "resources", "recruitment", "hiring", "training", "development", "performance", "evaluation", "feedback", "communication"], "science": ["science", "research", "study", "experiment", "hypothesis", "theory", "method", "analysis", "data", "result", "conclusion", "evidence", "observation", "measurement", "test", "sample", "control", "variable", "statistics", "probability", "correlation", "causation", "model", "simulation", "prediction", "validation", "verification", "peer", "review", "publication", "journal", "paper", "article", "conference", "presentation", "discovery", "innovation", "invention", "technology", "engineering", "design", "development", "prototype", "patent", "intellectual", "property", "copyright", "trademark", "license", "open", "source", "collaboration", "community", "knowledge", "education", "learning", "teaching", "university", "college", "school", "student", "professor", "researcher", "scientist", "engineer", "developer", "programmer", "analyst", "consultant", "expert", "specialist"]}