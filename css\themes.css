/* 
 * 主题样式文件
 * 包含明暗主题的颜色定义和切换逻辑
 */

/* 暗色主题变量 */
[data-theme="dark"] {
    /* 颜色变量重定义 */
    --primary-color: #60a5fa;
    --secondary-color: #9ca3af;
    --success-color: #34d399;
    --error-color: #f87171;
    --warning-color: #fbbf24;
    
    /* 背景颜色 */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    
    /* 文字颜色 */
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    
    /* 边框和阴影 */
    --border-color: #374151;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);
}

/* 主题切换动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 暗色主题下的特殊样式调整 */
[data-theme="dark"] .typing-input {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .typing-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(96 165 250 / 0.2);
}

/* 暗色主题下的文本高亮调整 */
[data-theme="dark"] .char-correct {
    background-color: var(--success-color);
    color: var(--bg-primary);
}

[data-theme="dark"] .char-incorrect {
    background-color: var(--error-color);
    color: var(--bg-primary);
}

[data-theme="dark"] .char-current {
    background-color: var(--primary-color);
    color: var(--bg-primary);
}

/* 暗色主题下的按钮样式调整 */
[data-theme="dark"] .btn-primary:hover {
    background-color: #3b82f6;
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: #4b5563;
}

/* 主题切换按钮图标 */
.theme-icon {
    font-size: 1.25rem;
    transition: transform 0.3s ease;
}

[data-theme="dark"] .theme-icon {
    transform: rotate(180deg);
}

/* 主题切换按钮内容切换 */
[data-theme="light"] .theme-icon::before {
    content: "🌙";
}

[data-theme="dark"] .theme-icon::before {
    content: "☀️";
}

/* 滚动条样式（暗色主题） */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #4b5563;
}

/* 明色主题滚动条样式 */
[data-theme="light"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="light"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
    background: #cbd5e1;
}

/* 选中文本的样式 */
[data-theme="dark"] ::selection {
    background-color: var(--primary-color);
    color: var(--bg-primary);
}

[data-theme="light"] ::selection {
    background-color: var(--primary-color);
    color: white;
}

/* 焦点指示器样式 */
[data-theme="dark"] *:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

[data-theme="light"] *:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 模态框在暗色主题下的样式 */
[data-theme="dark"] .modal {
    background-color: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .modal-content {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

/* 结果面板在暗色主题下的样式 */
[data-theme="dark"] .results-panel {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

/* 虚拟键盘在暗色主题下的样式 */
[data-theme="dark"] .virtual-keyboard {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .key {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .key:hover {
    background-color: #4b5563;
}

[data-theme="dark"] .key.active {
    background-color: var(--primary-color);
    color: var(--bg-primary);
}

/* 响应式主题调整 */
@media (max-width: 768px) {
    /* 在小屏幕上调整主题切换按钮 */
    .theme-icon {
        font-size: 1rem;
    }
}

/* 系统主题偏好检测 */
@media (prefers-color-scheme: dark) {
    :root {
        /* 如果用户系统偏好暗色主题，默认使用暗色变量 */
        --primary-color: #60a5fa;
        --secondary-color: #9ca3af;
        --success-color: #34d399;
        --error-color: #f87171;
        --warning-color: #fbbf24;
        
        --bg-primary: #111827;
        --bg-secondary: #1f2937;
        --bg-tertiary: #374151;
        
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        
        --border-color: #374151;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --shadow-sm: 0 2px 4px 0 rgb(0 0 0 / 0.8);
        --shadow-md: 0 6px 8px -1px rgb(0 0 0 / 0.8);
        --shadow-lg: 0 12px 16px -3px rgb(0 0 0 / 0.8);
    }
    
    .char-correct,
    .char-incorrect,
    .char-current {
        border: 2px solid #000000;
    }
    
    [data-theme="dark"] .char-correct,
    [data-theme="dark"] .char-incorrect,
    [data-theme="dark"] .char-current {
        border: 2px solid #ffffff;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
    
    .char-current {
        animation: none;
        opacity: 1;
    }
}
