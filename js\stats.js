/**
 * 统计功能模块
 * 负责计算和显示打字统计数据
 */

class StatsManager {
    constructor() {
        // 统计相关的DOM元素
        this.elements = {
            wpm: document.getElementById('wpm-display'),
            accuracy: document.getElementById('accuracy-display'),
            time: document.getElementById('time-display'),
            errors: document.getElementById('errors-display'),
            progress: document.getElementById('progress-fill'),
            progressText: document.getElementById('progress-text')
        };
        
        // 统计数据
        this.stats = {
            startTime: null,
            endTime: null,
            totalCharacters: 0,
            correctCharacters: 0,
            incorrectCharacters: 0,
            currentErrors: 0,
            totalErrors: 0,
            currentPosition: 0,
            isActive: false,
            isPaused: false,
            pausedTime: 0,
            lastPauseStart: null
        };
        
        // 计时器
        this.timer = null;
        this.updateInterval = 100; // 更新间隔（毫秒）
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化统计管理器
     */
    init() {
        this.resetStats();
        this.updateDisplay();
    }
    
    /**
     * 开始统计
     * @param {number} totalLength - 总字符数
     */
    start(totalLength = 0) {
        this.stats.startTime = Date.now();
        this.stats.totalCharacters = totalLength;
        this.stats.isActive = true;
        this.stats.isPaused = false;
        
        // 开始定时更新显示
        this.startTimer();
        
        console.log('统计开始:', this.stats);
    }
    
    /**
     * 暂停统计
     */
    pause() {
        if (this.stats.isActive && !this.stats.isPaused) {
            this.stats.isPaused = true;
            this.stats.lastPauseStart = Date.now();
            this.stopTimer();
            console.log('统计暂停');
        }
    }
    
    /**
     * 恢复统计
     */
    resume() {
        if (this.stats.isActive && this.stats.isPaused) {
            this.stats.isPaused = false;
            if (this.stats.lastPauseStart) {
                this.stats.pausedTime += Date.now() - this.stats.lastPauseStart;
                this.stats.lastPauseStart = null;
            }
            this.startTimer();
            console.log('统计恢复');
        }
    }
    
    /**
     * 停止统计
     */
    stop() {
        this.stats.isActive = false;
        this.stats.isPaused = false;
        this.stats.endTime = Date.now();
        this.stopTimer();
        
        console.log('统计结束:', this.getResults());
        return this.getResults();
    }
    
    /**
     * 重置统计数据
     */
    resetStats() {
        this.stats = {
            startTime: null,
            endTime: null,
            totalCharacters: 0,
            correctCharacters: 0,
            incorrectCharacters: 0,
            currentErrors: 0,
            totalErrors: 0,
            currentPosition: 0,
            isActive: false,
            isPaused: false,
            pausedTime: 0,
            lastPauseStart: null
        };
        
        this.stopTimer();
        this.updateDisplay();
    }
    
    /**
     * 更新字符统计
     * @param {boolean} isCorrect - 字符是否正确
     * @param {boolean} isBackspace - 是否为退格操作
     */
    updateCharacterStats(isCorrect, isBackspace = false) {
        if (!this.stats.isActive || this.stats.isPaused) return;
        
        if (isBackspace) {
            // 处理退格操作
            if (this.stats.currentPosition > 0) {
                this.stats.currentPosition--;
                if (this.stats.currentErrors > 0) {
                    this.stats.currentErrors--;
                }
            }
        } else {
            // 处理正常输入
            this.stats.currentPosition++;
            
            if (isCorrect) {
                this.stats.correctCharacters++;
                // 如果之前有错误，现在正确了，减少当前错误数
                if (this.stats.currentErrors > 0) {
                    this.stats.currentErrors--;
                }
            } else {
                this.stats.incorrectCharacters++;
                this.stats.currentErrors++;
                this.stats.totalErrors++;
            }
        }
        
        this.updateDisplay();
    }
    
    /**
     * 计算当前WPM（每分钟字数）
     * @returns {number} WPM值
     */
    calculateWPM() {
        if (!this.stats.startTime) return 0;
        
        const currentTime = this.stats.isPaused ? 
            this.stats.lastPauseStart : Date.now();
        const elapsedTime = (currentTime - this.stats.startTime - this.stats.pausedTime) / 1000 / 60;
        
        if (elapsedTime <= 0) return 0;
        
        // 标准WPM计算：正确字符数除以5（平均单词长度）再除以分钟数
        const words = this.stats.correctCharacters / 5;
        return Math.round(words / elapsedTime);
    }
    
    /**
     * 计算准确率
     * @returns {number} 准确率百分比
     */
    calculateAccuracy() {
        const totalTyped = this.stats.correctCharacters + this.stats.incorrectCharacters;
        if (totalTyped === 0) return 100;
        
        return Math.round((this.stats.correctCharacters / totalTyped) * 100);
    }
    
    /**
     * 计算已用时间（秒）
     * @returns {number} 已用时间
     */
    calculateElapsedTime() {
        if (!this.stats.startTime) return 0;
        
        const currentTime = this.stats.isPaused ? 
            this.stats.lastPauseStart : Date.now();
        return Math.round((currentTime - this.stats.startTime - this.stats.pausedTime) / 1000);
    }
    
    /**
     * 计算进度百分比
     * @returns {number} 进度百分比
     */
    calculateProgress() {
        if (this.stats.totalCharacters === 0) return 0;
        return Math.round((this.stats.currentPosition / this.stats.totalCharacters) * 100);
    }
    
    /**
     * 更新显示
     */
    updateDisplay() {
        // 更新WPM显示
        if (this.elements.wpm) {
            this.elements.wpm.textContent = this.calculateWPM();
        }
        
        // 更新准确率显示
        if (this.elements.accuracy) {
            this.elements.accuracy.textContent = this.calculateAccuracy();
        }
        
        // 更新时间显示
        if (this.elements.time) {
            this.elements.time.textContent = this.calculateElapsedTime();
        }
        
        // 更新错误数显示
        if (this.elements.errors) {
            this.elements.errors.textContent = this.stats.totalErrors;
        }
        
        // 更新进度条
        const progress = this.calculateProgress();
        if (this.elements.progress) {
            this.elements.progress.style.width = `${progress}%`;
        }
        if (this.elements.progressText) {
            this.elements.progressText.textContent = `${progress}%`;
        }
        
        // 根据统计数据添加视觉反馈
        this.updateVisualFeedback();
    }
    
    /**
     * 更新视觉反馈
     */
    updateVisualFeedback() {
        const accuracy = this.calculateAccuracy();
        const wpm = this.calculateWPM();
        
        // 根据准确率改变准确率显示颜色
        if (this.elements.accuracy) {
            this.elements.accuracy.className = 'stat-value';
            if (accuracy >= 95) {
                this.elements.accuracy.classList.add('text-success');
            } else if (accuracy >= 85) {
                this.elements.accuracy.classList.add('text-warning');
            } else {
                this.elements.accuracy.classList.add('text-error');
            }
        }
        
        // 根据WPM改变速度显示颜色
        if (this.elements.wpm) {
            this.elements.wpm.className = 'stat-value';
            if (wpm >= 60) {
                this.elements.wpm.classList.add('text-success');
            } else if (wpm >= 30) {
                this.elements.wpm.classList.add('text-warning');
            }
        }
    }
    
    /**
     * 开始计时器
     */
    startTimer() {
        this.stopTimer(); // 确保没有重复的计时器
        this.timer = setInterval(() => {
            this.updateDisplay();
        }, this.updateInterval);
    }
    
    /**
     * 停止计时器
     */
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    /**
     * 获取最终结果
     * @returns {Object} 包含所有统计数据的对象
     */
    getResults() {
        return {
            wpm: this.calculateWPM(),
            accuracy: this.calculateAccuracy(),
            time: this.calculateElapsedTime(),
            errors: this.stats.totalErrors,
            correctCharacters: this.stats.correctCharacters,
            incorrectCharacters: this.stats.incorrectCharacters,
            totalCharacters: this.stats.currentPosition,
            progress: this.calculateProgress(),
            startTime: this.stats.startTime,
            endTime: this.stats.endTime || Date.now()
        };
    }
    
    /**
     * 获取实时统计数据
     * @returns {Object} 当前统计数据
     */
    getCurrentStats() {
        return {
            wpm: this.calculateWPM(),
            accuracy: this.calculateAccuracy(),
            time: this.calculateElapsedTime(),
            errors: this.stats.totalErrors,
            progress: this.calculateProgress(),
            isActive: this.stats.isActive,
            isPaused: this.stats.isPaused
        };
    }
    
    /**
     * 设置总字符数（用于进度计算）
     * @param {number} total - 总字符数
     */
    setTotalCharacters(total) {
        this.stats.totalCharacters = total;
        this.updateDisplay();
    }
    
    /**
     * 检查是否达到目标
     * @param {Object} targets - 目标对象 {wpm, accuracy, time}
     * @returns {Object} 达成情况
     */
    checkTargets(targets = {}) {
        const current = this.getCurrentStats();
        const results = {};
        
        if (targets.wpm) {
            results.wpmAchieved = current.wpm >= targets.wpm;
        }
        
        if (targets.accuracy) {
            results.accuracyAchieved = current.accuracy >= targets.accuracy;
        }
        
        if (targets.time) {
            results.timeAchieved = current.time >= targets.time;
        }
        
        return results;
    }
}

// 创建全局统计管理器实例
const statsManager = new StatsManager();
