/**
 * 详细统计分析模块
 * 负责深度分析用户打字数据，提供详细的统计报告和可视化图表
 */

class AnalyticsManager {
    constructor() {
        // 分析数据存储
        this.analysisData = {
            characterErrors: new Map(), // 字符错误统计
            fingerUsage: new Map(),     // 手指使用统计
            speedHistory: [],           // 速度历史记录
            accuracyHistory: [],        // 准确率历史记录
            sessionData: [],            // 会话数据
            weakPoints: new Set(),      // 弱点字符
            strongPoints: new Set(),    // 强项字符
            timeAnalysis: {             // 时间段分析
                morning: { sessions: 0, avgWPM: 0, avgAccuracy: 0 },
                afternoon: { sessions: 0, avgWPM: 0, avgAccuracy: 0 },
                evening: { sessions: 0, avgWPM: 0, avgAccuracy: 0 },
                night: { sessions: 0, avgWPM: 0, avgAccuracy: 0 }
            }
        };
        
        // 分析配置
        this.config = {
            errorThreshold: 3,          // 错误阈值
            weakPointThreshold: 0.8,    // 弱点准确率阈值
            strongPointThreshold: 0.95, // 强项准确率阈值
            historyLimit: 100,          // 历史记录限制
            chartColors: {
                primary: '#3b82f6',
                success: '#10b981',
                warning: '#f59e0b',
                error: '#ef4444',
                secondary: '#6b7280'
            }
        };
        
        // DOM元素
        this.elements = {
            analyticsPanel: null,
            charts: new Map()
        };
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化分析管理器
     */
    init() {
        this.loadHistoricalData();
        this.createAnalyticsPanel();
        this.bindEvents();
        console.log('统计分析管理器初始化完成');
    }
    
    /**
     * 加载历史数据
     */
    loadHistoricalData() {
        if (window.storageManager) {
            const history = storageManager.getHistory();
            history.forEach(record => {
                this.processSessionData(record);
            });
            this.analyzeData();
        }
    }
    
    /**
     * 创建分析面板
     */
    createAnalyticsPanel() {
        // 检查是否已存在分析面板
        let panel = document.getElementById('analytics-panel');
        if (!panel) {
            panel = document.createElement('div');
            panel.id = 'analytics-panel';
            panel.className = 'analytics-panel hidden';
            document.body.appendChild(panel);
        }
        
        this.elements.analyticsPanel = panel;
        this.renderAnalyticsPanel();
    }
    
    /**
     * 渲染分析面板
     */
    renderAnalyticsPanel() {
        const panel = this.elements.analyticsPanel;
        if (!panel) return;
        
        panel.innerHTML = `
            <div class="analytics-header">
                <h2>详细统计分析</h2>
                <button id="close-analytics" class="btn btn-icon">✕</button>
            </div>
            
            <div class="analytics-content">
                <!-- 总览统计 -->
                <div class="analytics-section">
                    <h3>总览统计</h3>
                    <div class="overview-stats">
                        <div class="overview-item">
                            <span class="overview-label">总练习次数</span>
                            <span class="overview-value" id="total-sessions">0</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">总练习时间</span>
                            <span class="overview-value" id="total-time">0分钟</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">平均速度</span>
                            <span class="overview-value" id="avg-wpm">0 WPM</span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">平均准确率</span>
                            <span class="overview-value" id="avg-accuracy">0%</span>
                        </div>
                    </div>
                </div>
                
                <!-- 进度趋势图 -->
                <div class="analytics-section">
                    <h3>进度趋势</h3>
                    <div class="chart-container">
                        <canvas id="progress-chart" width="400" height="200"></canvas>
                    </div>
                </div>
                
                <!-- 错误分析 -->
                <div class="analytics-section">
                    <h3>错误分析</h3>
                    <div class="error-analysis">
                        <div class="error-chart">
                            <canvas id="error-chart" width="300" height="200"></canvas>
                        </div>
                        <div class="error-details">
                            <h4>常见错误字符</h4>
                            <div id="error-characters" class="error-list"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 手指使用分析 -->
                <div class="analytics-section">
                    <h3>手指使用分析</h3>
                    <div class="finger-analysis">
                        <canvas id="finger-chart" width="400" height="200"></canvas>
                        <div class="finger-recommendations" id="finger-recommendations"></div>
                    </div>
                </div>
                
                <!-- 时间段分析 -->
                <div class="analytics-section">
                    <h3>时间段表现</h3>
                    <div class="time-analysis">
                        <canvas id="time-chart" width="400" height="200"></canvas>
                    </div>
                </div>
                
                <!-- 学习建议 -->
                <div class="analytics-section">
                    <h3>学习建议</h3>
                    <div id="learning-suggestions" class="suggestions-list"></div>
                </div>
            </div>
        `;
        
        this.updateAnalyticsData();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭分析面板
        document.addEventListener('click', (e) => {
            if (e.target.id === 'close-analytics') {
                this.hideAnalytics();
            }
        });
        
        // 监听练习完成事件
        document.addEventListener('practice:completed', (e) => {
            if (e.detail) {
                this.processSessionData(e.detail);
                this.analyzeData();
            }
        });
    }
    
    /**
     * 处理会话数据
     * @param {Object} sessionData - 会话数据
     */
    processSessionData(sessionData) {
        // 添加到会话历史
        this.analysisData.sessionData.push({
            ...sessionData,
            timestamp: sessionData.timestamp || Date.now()
        });
        
        // 限制历史记录数量
        if (this.analysisData.sessionData.length > this.config.historyLimit) {
            this.analysisData.sessionData.shift();
        }
        
        // 更新速度和准确率历史
        this.analysisData.speedHistory.push({
            timestamp: sessionData.timestamp || Date.now(),
            wpm: sessionData.wpm || 0
        });
        
        this.analysisData.accuracyHistory.push({
            timestamp: sessionData.timestamp || Date.now(),
            accuracy: sessionData.accuracy || 0
        });
        
        // 分析时间段
        this.analyzeTimeOfDay(sessionData);
        
        // 分析字符错误（如果有详细数据）
        if (sessionData.characterErrors) {
            this.analyzeCharacterErrors(sessionData.characterErrors);
        }
        
        // 分析手指使用（如果有详细数据）
        if (sessionData.fingerUsage) {
            this.analyzeFingerUsage(sessionData.fingerUsage);
        }
    }
    
    /**
     * 分析时间段表现
     * @param {Object} sessionData - 会话数据
     */
    analyzeTimeOfDay(sessionData) {
        const hour = new Date(sessionData.timestamp || Date.now()).getHours();
        let period;
        
        if (hour >= 6 && hour < 12) period = 'morning';
        else if (hour >= 12 && hour < 18) period = 'afternoon';
        else if (hour >= 18 && hour < 22) period = 'evening';
        else period = 'night';
        
        const timeData = this.analysisData.timeAnalysis[period];
        const sessions = timeData.sessions;
        
        timeData.avgWPM = (timeData.avgWPM * sessions + (sessionData.wpm || 0)) / (sessions + 1);
        timeData.avgAccuracy = (timeData.avgAccuracy * sessions + (sessionData.accuracy || 0)) / (sessions + 1);
        timeData.sessions += 1;
    }
    
    /**
     * 分析字符错误
     * @param {Object} characterErrors - 字符错误数据
     */
    analyzeCharacterErrors(characterErrors) {
        Object.entries(characterErrors).forEach(([char, errorCount]) => {
            const currentCount = this.analysisData.characterErrors.get(char) || 0;
            this.analysisData.characterErrors.set(char, currentCount + errorCount);
        });
    }
    
    /**
     * 分析手指使用
     * @param {Object} fingerUsage - 手指使用数据
     */
    analyzeFingerUsage(fingerUsage) {
        Object.entries(fingerUsage).forEach(([finger, usage]) => {
            const currentUsage = this.analysisData.fingerUsage.get(finger) || { correct: 0, total: 0 };
            this.analysisData.fingerUsage.set(finger, {
                correct: currentUsage.correct + usage.correct,
                total: currentUsage.total + usage.total
            });
        });
    }
    
    /**
     * 分析数据并生成洞察
     */
    analyzeData() {
        this.identifyWeakPoints();
        this.identifyStrongPoints();
        this.generateLearningInsights();
    }
    
    /**
     * 识别弱点
     */
    identifyWeakPoints() {
        this.analysisData.weakPoints.clear();
        
        // 基于错误频率识别弱点字符
        this.analysisData.characterErrors.forEach((errorCount, char) => {
            if (errorCount >= this.config.errorThreshold) {
                this.analysisData.weakPoints.add(char);
            }
        });
        
        // 基于手指准确率识别弱点手指
        this.analysisData.fingerUsage.forEach((usage, finger) => {
            const accuracy = usage.total > 0 ? usage.correct / usage.total : 1;
            if (accuracy < this.config.weakPointThreshold) {
                this.analysisData.weakPoints.add(`finger-${finger}`);
            }
        });
    }
    
    /**
     * 识别强项
     */
    identifyStrongPoints() {
        this.analysisData.strongPoints.clear();
        
        // 基于手指准确率识别强项手指
        this.analysisData.fingerUsage.forEach((usage, finger) => {
            const accuracy = usage.total > 0 ? usage.correct / usage.total : 1;
            if (accuracy >= this.config.strongPointThreshold && usage.total >= 10) {
                this.analysisData.strongPoints.add(`finger-${finger}`);
            }
        });
    }
    
    /**
     * 生成学习洞察
     */
    generateLearningInsights() {
        const insights = [];
        
        // 基于弱点生成建议
        if (this.analysisData.weakPoints.size > 0) {
            insights.push({
                type: 'warning',
                title: '需要改进的地方',
                content: `您在以下方面需要加强练习：${Array.from(this.analysisData.weakPoints).join(', ')}`
            });
        }
        
        // 基于强项生成鼓励
        if (this.analysisData.strongPoints.size > 0) {
            insights.push({
                type: 'success',
                title: '您的优势',
                content: `您在以下方面表现优秀：${Array.from(this.analysisData.strongPoints).join(', ')}`
            });
        }
        
        // 基于时间段分析生成建议
        const bestPeriod = this.getBestPerformancePeriod();
        if (bestPeriod) {
            insights.push({
                type: 'info',
                title: '最佳练习时间',
                content: `您在${bestPeriod}时段表现最佳，建议在此时间多加练习。`
            });
        }
        
        this.learningInsights = insights;
    }
    
    /**
     * 获取最佳表现时间段
     * @returns {string} 最佳时间段
     */
    getBestPerformancePeriod() {
        let bestPeriod = null;
        let bestScore = 0;
        
        Object.entries(this.analysisData.timeAnalysis).forEach(([period, data]) => {
            if (data.sessions > 0) {
                const score = (data.avgWPM * 0.6 + data.avgAccuracy * 0.4) * Math.log(data.sessions + 1);
                if (score > bestScore) {
                    bestScore = score;
                    bestPeriod = period;
                }
            }
        });
        
        const periodNames = {
            morning: '上午',
            afternoon: '下午',
            evening: '晚上',
            night: '深夜'
        };
        
        return bestPeriod ? periodNames[bestPeriod] : null;
    }
    
    /**
     * 更新分析数据显示
     */
    updateAnalyticsData() {
        if (!this.elements.analyticsPanel) return;
        
        // 更新总览统计
        const totalSessions = this.analysisData.sessionData.length;
        const totalTime = this.analysisData.sessionData.reduce((sum, session) => sum + (session.time || 0), 0);
        const avgWPM = totalSessions > 0 ? 
            Math.round(this.analysisData.sessionData.reduce((sum, session) => sum + (session.wpm || 0), 0) / totalSessions) : 0;
        const avgAccuracy = totalSessions > 0 ? 
            Math.round(this.analysisData.sessionData.reduce((sum, session) => sum + (session.accuracy || 0), 0) / totalSessions) : 0;
        
        this.updateElement('total-sessions', totalSessions);
        this.updateElement('total-time', Math.round(totalTime / 60) + '分钟');
        this.updateElement('avg-wpm', avgWPM + ' WPM');
        this.updateElement('avg-accuracy', avgAccuracy + '%');
        
        // 更新图表
        this.updateCharts();
        
        // 更新学习建议
        this.updateLearningSuggestions();
    }
    
    /**
     * 更新元素内容
     * @param {string} id - 元素ID
     * @param {string} content - 内容
     */
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }
    
    /**
     * 更新图表
     */
    updateCharts() {
        // 这里可以集成图表库如Chart.js来绘制图表
        // 由于项目要求使用原生JS，我们创建简单的文本图表
        this.createSimpleProgressChart();
        this.createSimpleErrorChart();
        this.createSimpleFingerChart();
        this.createSimpleTimeChart();
    }
    
    /**
     * 创建简单进度图表
     */
    createSimpleProgressChart() {
        const canvas = document.getElementById('progress-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制简单的折线图
        if (this.analysisData.speedHistory.length > 1) {
            ctx.strokeStyle = this.config.chartColors.primary;
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const maxWPM = Math.max(...this.analysisData.speedHistory.map(h => h.wpm));
            const minWPM = Math.min(...this.analysisData.speedHistory.map(h => h.wpm));
            const range = maxWPM - minWPM || 1;
            
            this.analysisData.speedHistory.forEach((point, index) => {
                const x = (index / (this.analysisData.speedHistory.length - 1)) * (canvas.width - 40) + 20;
                const y = canvas.height - 20 - ((point.wpm - minWPM) / range) * (canvas.height - 40);
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
        }
        
        // 添加标题
        ctx.fillStyle = this.config.chartColors.secondary;
        ctx.font = '14px Arial';
        ctx.fillText('速度趋势 (WPM)', 10, 15);
    }
    
    /**
     * 创建简单错误图表
     */
    createSimpleErrorChart() {
        const errorList = document.getElementById('error-characters');
        if (!errorList) return;
        
        errorList.innerHTML = '';
        
        // 获取错误最多的前5个字符
        const sortedErrors = Array.from(this.analysisData.characterErrors.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);
        
        sortedErrors.forEach(([char, count]) => {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.innerHTML = `
                <span class="error-char">${char === ' ' ? '空格' : char}</span>
                <span class="error-count">${count}次</span>
            `;
            errorList.appendChild(errorItem);
        });
        
        if (sortedErrors.length === 0) {
            errorList.innerHTML = '<div class="no-data">暂无错误数据</div>';
        }
    }
    
    /**
     * 创建简单手指图表
     */
    createSimpleFingerChart() {
        const recommendations = document.getElementById('finger-recommendations');
        if (!recommendations) return;
        
        recommendations.innerHTML = '';
        
        // 分析手指表现
        const fingerPerformance = [];
        this.analysisData.fingerUsage.forEach((usage, finger) => {
            if (usage.total > 0) {
                const accuracy = usage.correct / usage.total;
                fingerPerformance.push({ finger, accuracy, total: usage.total });
            }
        });
        
        // 排序并显示建议
        fingerPerformance.sort((a, b) => a.accuracy - b.accuracy);
        
        if (fingerPerformance.length > 0) {
            const weakestFinger = fingerPerformance[0];
            if (weakestFinger.accuracy < 0.9) {
                const recommendation = document.createElement('div');
                recommendation.className = 'finger-recommendation';
                recommendation.innerHTML = `
                    <strong>建议：</strong>加强${weakestFinger.finger}的练习，当前准确率：${Math.round(weakestFinger.accuracy * 100)}%
                `;
                recommendations.appendChild(recommendation);
            }
        } else {
            recommendations.innerHTML = '<div class="no-data">暂无手指使用数据</div>';
        }
    }
    
    /**
     * 创建简单时间图表
     */
    createSimpleTimeChart() {
        const canvas = document.getElementById('time-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制时间段表现柱状图
        const periods = ['morning', 'afternoon', 'evening', 'night'];
        const periodNames = ['上午', '下午', '晚上', '深夜'];
        const barWidth = canvas.width / periods.length - 20;
        
        periods.forEach((period, index) => {
            const data = this.analysisData.timeAnalysis[period];
            if (data.sessions > 0) {
                const height = (data.avgWPM / 100) * (canvas.height - 60);
                const x = index * (barWidth + 20) + 10;
                const y = canvas.height - 40 - height;
                
                ctx.fillStyle = this.config.chartColors.primary;
                ctx.fillRect(x, y, barWidth, height);
                
                // 添加标签
                ctx.fillStyle = this.config.chartColors.secondary;
                ctx.font = '12px Arial';
                ctx.fillText(periodNames[index], x, canvas.height - 20);
                ctx.fillText(`${Math.round(data.avgWPM)}`, x, y - 5);
            }
        });
        
        // 添加标题
        ctx.fillStyle = this.config.chartColors.secondary;
        ctx.font = '14px Arial';
        ctx.fillText('时间段平均速度 (WPM)', 10, 15);
    }
    
    /**
     * 更新学习建议
     */
    updateLearningSuggestions() {
        const suggestionsContainer = document.getElementById('learning-suggestions');
        if (!suggestionsContainer) return;
        
        suggestionsContainer.innerHTML = '';
        
        if (this.learningInsights && this.learningInsights.length > 0) {
            this.learningInsights.forEach(insight => {
                const suggestionElement = document.createElement('div');
                suggestionElement.className = `suggestion suggestion-${insight.type}`;
                suggestionElement.innerHTML = `
                    <h4>${insight.title}</h4>
                    <p>${insight.content}</p>
                `;
                suggestionsContainer.appendChild(suggestionElement);
            });
        } else {
            suggestionsContainer.innerHTML = '<div class="no-data">继续练习以获得个性化建议</div>';
        }
    }
    
    /**
     * 显示分析面板
     */
    showAnalytics() {
        if (this.elements.analyticsPanel) {
            this.updateAnalyticsData();
            this.elements.analyticsPanel.classList.remove('hidden');
        }
    }
    
    /**
     * 隐藏分析面板
     */
    hideAnalytics() {
        if (this.elements.analyticsPanel) {
            this.elements.analyticsPanel.classList.add('hidden');
        }
    }
    
    /**
     * 获取分析报告
     * @returns {Object} 分析报告
     */
    getAnalysisReport() {
        return {
            overview: {
                totalSessions: this.analysisData.sessionData.length,
                totalTime: this.analysisData.sessionData.reduce((sum, session) => sum + (session.time || 0), 0),
                averageWPM: this.analysisData.sessionData.length > 0 ? 
                    this.analysisData.sessionData.reduce((sum, session) => sum + (session.wpm || 0), 0) / this.analysisData.sessionData.length : 0,
                averageAccuracy: this.analysisData.sessionData.length > 0 ? 
                    this.analysisData.sessionData.reduce((sum, session) => sum + (session.accuracy || 0), 0) / this.analysisData.sessionData.length : 0
            },
            weakPoints: Array.from(this.analysisData.weakPoints),
            strongPoints: Array.from(this.analysisData.strongPoints),
            timeAnalysis: this.analysisData.timeAnalysis,
            insights: this.learningInsights || []
        };
    }
}

// 创建全局分析管理器实例
const analyticsManager = new AnalyticsManager();
